package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) UserQuery(ctx context.Context, req *v1.UserQueryReq) (res *v1.UserQueryRes, err error) {
	// 1. 调用业务逻辑 - 直接通过用户ID获取用户信息
	userInfo, err := service.User().GetUserByID(ctx, req.UserId)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 2. 参数转换：业务层Output → API响应
	res = &v1.UserQueryRes{
		Id:         userInfo.Id,
		Username:   userInfo.Username,
		Email:      userInfo.Email,
		Role:       userInfo.Role,
		Status:     userInfo.Status,
		CreateTime: userInfo.CreateTime.String(),
		AvatarUrl:  userInfo.AvatarUrl,
		Phone:      userInfo.Phone,
	}

	return res, nil
}

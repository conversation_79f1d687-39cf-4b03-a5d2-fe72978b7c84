package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) UserCreate(ctx context.Context, req *v1.UserCreateReq) (res *v1.UserCreateRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.UserCreateInput{
		Username:  req.Username,
		Email:     req.Email,
		Password:  req.Password,
		Role:      req.Role,
		Status:    req.Status,
		AvatarUrl: req.AvatarUrl,
		Openid:    req.Openid,
	}

	// 2. 调用业务逻辑
	userInfo, err := service.User().CreateUser(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.UserCreateRes{
		Id:         userInfo.Id,
		Username:   userInfo.Username,
		Email:      userInfo.Email,
		Role:       userInfo.Role,
		Status:     userInfo.Status,
		CreateTime: userInfo.CreateTime.String(),
		AvatarUrl:  userInfo.AvatarUrl,
	}

	return res, nil
}

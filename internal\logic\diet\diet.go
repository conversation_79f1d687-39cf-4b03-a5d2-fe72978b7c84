package diet

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"
	"shikeyinxiang-goframe/internal/dao"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/model/do"
	"shikeyinxiang-goframe/internal/model/entity"
	"shikeyinxiang-goframe/internal/service"
)

type sDiet struct{}

func init() {
	service.RegisterDiet(New())
}

func New() service.IDiet {
	return &sDiet{}
}

// CreateDietRecord 添加饮食记录
func (s *sDiet) CreateDietRecord(ctx context.Context, in *model.DietRecordCreateInput) (*model.DietRecordCreateOutput, error) {
	// 参数验证
	if in.UserId <= 0 {
		return nil, gerror.NewCode(CodeDietRecordInvalidUser, "用户ID无效")
	}
	if in.Date == "" {
		return nil, gerror.NewCode(CodeDietRecordInvalidDate, "记录日期不能为空")
	}
	if in.Time == "" {
		return nil, gerror.NewCode(CodeDietRecordInvalidTime, "记录时间不能为空")
	}
	if !isValidMealType(in.MealType) {
		return nil, gerror.NewCode(CodeDietMealTypeInvalid, "餐次类型无效")
	}
	if len(in.Foods) == 0 {
		return nil, gerror.NewCode(CodeDietFoodListEmpty, "食物列表不能为空")
	}

	// 验证日期格式
	date, err := time.Parse("2006-01-02", in.Date)
	if err != nil {
		return nil, gerror.NewCode(CodeDietRecordInvalidDate, "记录日期格式无效")
	}

	// 验证时间格式
	timeValue, err := time.Parse("15:04:05", in.Time)
	if err != nil {
		return nil, gerror.NewCode(CodeDietRecordInvalidTime, "记录时间格式无效")
	}

	// 验证食物数据
	for _, food := range in.Foods {
		if food.FoodId <= 0 {
			return nil, gerror.NewCode(CodeDietFoodNotFound, "食物ID无效")
		}
		if food.Amount <= 0 {
			return nil, gerror.NewCode(CodeDietQuantityInvalid, "食物数量必须大于0")
		}
	}

	var recordId int64
	err = dao.DietRecords.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 插入饮食记录主表
		result, err := dao.DietRecords.Ctx(ctx).TX(tx).Data(do.DietRecords{
			UserId:       in.UserId,
			Date:         gtime.New(date),
			Time:         gtime.New(timeValue),
			MealType:     in.MealType,
			Remark:       in.Remark,
			TotalCalorie: in.TotalCalorie,
			CreatedAt:    gtime.Now(),
			UpdatedAt:    gtime.Now(),
		}).Insert()
		if err != nil {
			return err
		}

		id, err := result.LastInsertId()
		if err != nil {
			return err
		}
		recordId = id

		// 插入食物明细
		for _, food := range in.Foods {
			_, err = dao.DietRecordFoods.Ctx(ctx).TX(tx).Data(do.DietRecordFoods{
				DietRecordId: recordId,
				FoodId:       food.FoodId,
				FoodName:     food.Name,
				Amount:       food.Amount,
				Unit:         food.Unit,
				Calories:     food.Calories,
				Protein:      food.Protein,
				Fat:          food.Fat,
				Carbs:        food.Carbs,
				Grams:        food.Grams,
				CreatedAt:    gtime.Now(),
			}).Insert()
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, gerror.NewCode(CodeDietRecordCreateFailed, "创建饮食记录失败")
	}

	return &model.DietRecordCreateOutput{
		RecordId: recordId,
	}, nil
}

// GetDietRecords 获取用户饮食记录列表
func (s *sDiet) GetDietRecords(ctx context.Context, in *model.DietRecordQueryInput) (*model.DietRecordQueryOutput, error) {
	if in.UserId <= 0 {
		return nil, gerror.NewCode(CodeDietRecordInvalidUser, "用户ID无效")
	}

	// 设置默认分页参数
	if in.Page <= 0 {
		in.Page = 1
	}
	if in.Size <= 0 {
		in.Size = 10
	}

	// 构建查询条件
	query := dao.DietRecords.Ctx(ctx).Where(dao.DietRecords.Columns().UserId, in.UserId)

	// 日期范围过滤
	if in.StartDate != "" {
		query = query.WhereGTE(dao.DietRecords.Columns().Date, in.StartDate)
	}
	if in.EndDate != "" {
		query = query.WhereLTE(dao.DietRecords.Columns().Date, in.EndDate)
	}

	// 餐次类型过滤
	if in.MealType != "" {
		query = query.Where(dao.DietRecords.Columns().MealType, in.MealType)
	}

	// 查询总数
	total, err := query.Count()
	if err != nil {
		return nil, gerror.NewCode(CodeDietRecordQueryFailed, "查询饮食记录失败")
	}

	// 分页查询
	var records []entity.DietRecords
	err = query.OrderDesc(dao.DietRecords.Columns().Date).OrderDesc(dao.DietRecords.Columns().Time).
		Page(in.Page, in.Size).
		Scan(&records)
	if err != nil {
		return nil, gerror.NewCode(CodeDietRecordQueryFailed, "查询饮食记录失败")
	}

	// 转换为输出格式
	list, err := s.convertToRecordItems(ctx, records)
	if err != nil {
		return nil, err
	}

	return &model.DietRecordQueryOutput{
		List:  list,
		Page:  in.Page,
		Size:  in.Size,
		Total: total,
	}, nil
}

// GetAllUsersDietRecords 获取所有用户饮食记录列表(管理员)
func (s *sDiet) GetAllUsersDietRecords(ctx context.Context, in *model.DietRecordAdminQueryInput) (*model.DietRecordAdminQueryOutput, error) {
	// 设置默认分页参数
	if in.Page <= 0 {
		in.Page = 1
	}
	if in.Size <= 0 {
		in.Size = 10
	}

	// 构建查询条件
	query := dao.DietRecords.Ctx(ctx)

	// 用户ID过滤(可选)
	if in.UserId != nil && *in.UserId > 0 {
		query = query.Where(dao.DietRecords.Columns().UserId, *in.UserId)
	}

	// 日期范围过滤
	if in.StartDate != "" {
		query = query.WhereGTE(dao.DietRecords.Columns().Date, in.StartDate)
	}
	if in.EndDate != "" {
		query = query.WhereLTE(dao.DietRecords.Columns().Date, in.EndDate)
	}

	// 餐次类型过滤
	if in.MealType != "" {
		query = query.Where(dao.DietRecords.Columns().MealType, in.MealType)
	}

	// 查询总数
	total, err := query.Count()
	if err != nil {
		return nil, gerror.NewCode(CodeDietRecordQueryFailed, "查询饮食记录失败")
	}

	// 分页查询
	var records []entity.DietRecords
	err = query.OrderDesc(dao.DietRecords.Columns().Date).OrderDesc(dao.DietRecords.Columns().Time).
		Page(in.Page, in.Size).
		Scan(&records)
	if err != nil {
		return nil, gerror.NewCode(CodeDietRecordQueryFailed, "查询饮食记录失败")
	}

	// 转换为输出格式
	list, err := s.convertToRecordItems(ctx, records)
	if err != nil {
		return nil, err
	}

	return &model.DietRecordAdminQueryOutput{
		List:  list,
		Page:  in.Page,
		Size:  in.Size,
		Total: total,
	}, nil
}

// GetDietRecordDetail 获取饮食记录详情
func (s *sDiet) GetDietRecordDetail(ctx context.Context, in *model.DietRecordDetailInput) (*model.DietRecordDetailOutput, error) {
	if in.RecordId <= 0 {
		return nil, gerror.NewCode(CodeDietRecordNotFound, "饮食记录ID无效")
	}

	// 构建查询条件
	query := dao.DietRecords.Ctx(ctx).Where(dao.DietRecords.Columns().Id, in.RecordId)

	// 如果指定了用户ID，则验证权限
	if in.UserId != nil {
		query = query.Where(dao.DietRecords.Columns().UserId, *in.UserId)
	}

	var record entity.DietRecords
	err := query.Scan(&record)
	if err != nil {
		return nil, gerror.NewCode(CodeDietRecordQueryFailed, "查询饮食记录失败")
	}

	if record.Id == 0 {
		return nil, gerror.NewCode(CodeDietRecordNotFound, "饮食记录不存在")
	}

	// 转换为输出格式
	items, err := s.convertToRecordItems(ctx, []entity.DietRecords{record})
	if err != nil {
		return nil, err
	}

	if len(items) == 0 {
		return nil, gerror.NewCode(CodeDietRecordNotFound)
	}

	return &model.DietRecordDetailOutput{
		Record: items[0],
	}, nil
}

// DeleteDietRecord 删除饮食记录
func (s *sDiet) DeleteDietRecord(ctx context.Context, in *model.DietRecordDeleteInput) (*model.DietRecordDeleteOutput, error) {
	if in.RecordId <= 0 {
		return nil, gerror.NewCode(CodeDietRecordNotFound, "饮食记录ID无效")
	}

	err := dao.DietRecords.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 验证记录是否存在
		query := dao.DietRecords.Ctx(ctx).TX(tx).Where(dao.DietRecords.Columns().Id, in.RecordId)

		// 如果指定了用户ID，则验证权限
		if in.UserId != nil {
			query = query.Where(dao.DietRecords.Columns().UserId, *in.UserId)
		}

		var record entity.DietRecords
		err := query.Scan(&record)
		if err != nil {
			return err
		}

		if record.Id == 0 {
			return gerror.NewCode(CodeDietRecordNotFound, "饮食记录不存在")
		}

		// 删除食物记录
		_, err = dao.DietRecordFoods.Ctx(ctx).TX(tx).Where(dao.DietRecordFoods.Columns().DietRecordId, in.RecordId).Delete()
		if err != nil {
			return err
		}

		// 删除主记录
		_, err = dao.DietRecords.Ctx(ctx).TX(tx).Where(dao.DietRecords.Columns().Id, in.RecordId).Delete()
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		if gerror.HasCode(err, CodeDietRecordNotFound) {
			return &model.DietRecordDeleteOutput{Success: false}, nil
		}
		return nil, gerror.NewCode(CodeDietRecordDeleteFailed, "删除饮食记录失败")
	}

	return &model.DietRecordDeleteOutput{Success: true}, nil
}

// CountDietRecordsByDate 统计指定日期的记录数量
func (s *sDiet) CountDietRecordsByDate(ctx context.Context, in *model.DietRecordCountByDateInput) (*model.DietRecordCountByDateOutput, error) {
	if in.Date == "" {
		return nil, gerror.NewCode(CodeDietRecordInvalidDate, "日期不能为空")
	}

	count, err := dao.DietRecords.Ctx(ctx).Where(dao.DietRecords.Columns().Date, in.Date).Count()
	if err != nil {
		return nil, gerror.NewCode(CodeDietStatsCalculationFailed, "统计计算失败")
	}

	return &model.DietRecordCountByDateOutput{
		Count: count,
	}, nil
}

// GetActiveUserIds 获取活跃用户ID列表
func (s *sDiet) GetActiveUserIds(ctx context.Context, in *model.DietRecordActiveUsersInput) (*model.DietRecordActiveUsersOutput, error) {
	query := dao.DietRecords.Ctx(ctx).Fields("DISTINCT user_id")

	// 根据输入参数构建查询条件
	if in.Date != nil && *in.Date != "" {
		// 查询指定日期
		query = query.Where(dao.DietRecords.Columns().Date, *in.Date)
	} else if in.StartDate != nil && in.EndDate != nil && *in.StartDate != "" && *in.EndDate != "" {
		// 查询日期范围
		query = query.WhereBetween(dao.DietRecords.Columns().Date, *in.StartDate, *in.EndDate)
	} else {
		return nil, gerror.NewCode(CodeDietRecordInvalidDate, "日期参数无效")
	}

	var userIds []int64
	err := query.Scan(&userIds)
	if err != nil {
		return nil, gerror.NewCode(CodeDietStatsCalculationFailed, "统计计算失败")
	}

	return &model.DietRecordActiveUsersOutput{
		UserIds: userIds,
	}, nil
}

// GetPopularFoods 获取热门食物统计
func (s *sDiet) GetPopularFoods(ctx context.Context, in *model.DietRecordPopularFoodsInput) (*model.DietRecordPopularFoodsOutput, error) {
	if in.Period == "" {
		return nil, gerror.NewCode(CodeDietStatsParameterInvalid, "时间周期参数不能为空")
	}
	if in.Limit <= 0 {
		in.Limit = 10
	}

	// 计算日期范围
	now := time.Now()
	var startDate, endDate time.Time

	switch in.Period {
	case "week":
		startDate = now.AddDate(0, 0, -7)
		endDate = now
	case "month":
		startDate = now.AddDate(0, -1, 0)
		endDate = now
	case "quarter":
		startDate = now.AddDate(0, -3, 0)
		endDate = now
	default:
		return nil, gerror.NewCode(CodeDietStatsParameterInvalid, "不支持的时间周期")
	}

	// 使用DAO构建查询
	type PopularFoodResult struct {
		FoodName string `json:"food_name"`
		Count    int    `json:"count"`
	}

	var results []PopularFoodResult
	err := dao.DietRecordFoods.Ctx(ctx).
		Fields(dao.DietRecordFoods.Columns().FoodName+", COUNT(*) as count").
		LeftJoin(dao.DietRecords.Table()+" dr", dao.DietRecordFoods.Columns().DietRecordId+" = dr."+dao.DietRecords.Columns().Id).
		WhereBetween("dr."+dao.DietRecords.Columns().Date, startDate.Format("2006-01-02"), endDate.Format("2006-01-02")).
		Group(dao.DietRecordFoods.Columns().FoodName).
		OrderDesc("count").
		Limit(in.Limit).
		Scan(&results)
	if err != nil {
		return nil, gerror.NewCode(CodeDietStatsCalculationFailed, "统计计算失败")
	}

	var foods []model.PopularFoodItem
	for _, result := range results {
		foods = append(foods, model.PopularFoodItem{
			FoodName: result.FoodName,
			Count:    result.Count,
		})
	}

	return &model.DietRecordPopularFoodsOutput{
		Foods: foods,
	}, nil
}

// GetBatchDietRecordsForNutritionStat 批量获取饮食记录(供营养统计使用)
func (s *sDiet) GetBatchDietRecordsForNutritionStat(ctx context.Context, in *model.DietRecordBatchQueryInput) (*model.DietRecordBatchQueryOutput, error) {
	if len(in.UserIds) == 0 {
		return &model.DietRecordBatchQueryOutput{
			Records: make(map[int64]map[string][]model.DietRecordItem),
		}, nil
	}

	if in.StartDate == "" || in.EndDate == "" {
		return nil, gerror.NewCode(CodeDietRecordInvalidDate, "开始日期和结束日期不能为空")
	}

	// 查询指定用户在指定日期范围内的所有饮食记录
	var records []entity.DietRecords
	err := dao.DietRecords.Ctx(ctx).
		WhereIn(dao.DietRecords.Columns().UserId, in.UserIds).
		WhereBetween(dao.DietRecords.Columns().Date, in.StartDate, in.EndDate).
		OrderDesc(dao.DietRecords.Columns().Date).OrderDesc(dao.DietRecords.Columns().Time).
		Scan(&records)
	if err != nil {
		return nil, gerror.NewCode(CodeDietBatchQueryFailed, "批量查询失败")
	}

	// 转换为输出格式
	items, err := s.convertToRecordItems(ctx, records)
	if err != nil {
		return nil, err
	}

	// 按用户ID和日期分组
	result := make(map[int64]map[string][]model.DietRecordItem)
	for _, item := range items {
		userId := item.UserId
		dateStr := item.Date

		if result[userId] == nil {
			result[userId] = make(map[string][]model.DietRecordItem)
		}
		if result[userId][dateStr] == nil {
			result[userId][dateStr] = make([]model.DietRecordItem, 0)
		}
		result[userId][dateStr] = append(result[userId][dateStr], item)
	}

	return &model.DietRecordBatchQueryOutput{
		Records: result,
	}, nil
}

// convertToRecordItems 转换为记录项列表
func (s *sDiet) convertToRecordItems(ctx context.Context, records []entity.DietRecords) ([]model.DietRecordItem, error) {
	if len(records) == 0 {
		return []model.DietRecordItem{}, nil
	}

	// 提取记录ID列表
	recordIds := make([]int64, len(records))
	userIds := make([]int64, 0, len(records))
	userIdMap := make(map[int64]bool)

	for i, record := range records {
		recordIds[i] = record.Id
		if !userIdMap[record.UserId] {
			userIds = append(userIds, record.UserId)
			userIdMap[record.UserId] = true
		}
	}

	// 批量查询食物明细
	var foods []entity.DietRecordFoods
	err := dao.DietRecordFoods.Ctx(ctx).WhereIn(dao.DietRecordFoods.Columns().DietRecordId, recordIds).Scan(&foods)
	if err != nil {
		return nil, gerror.NewCode(CodeDietRecordQueryFailed, "查询饮食记录失败")
	}

	// 按记录ID分组食物明细
	foodsMap := make(map[int64][]model.DietRecordFood)
	for _, food := range foods {
		foodsMap[food.DietRecordId] = append(foodsMap[food.DietRecordId], model.DietRecordFood{
			FoodId:   food.FoodId,
			Name:     food.FoodName,
			Amount:   food.Amount,
			Unit:     food.Unit,
			Calories: food.Calories,
			Protein:  food.Protein,
			Fat:      food.Fat,
			Carbs:    food.Carbs,
			Grams:    food.Grams,
		})
	}

	// 批量查询用户信息
	userMap := make(map[int64]string)
	if len(userIds) > 0 {
		userInfos, err := service.User().GetUsersByIds(ctx, &model.UserBatchQueryInput{UserIds: userIds})
		if err != nil {
		} else {
			for _, user := range userInfos.Users {
				userMap[user.Id] = user.Username
			}
		}
	}

	// 转换为输出格式
	var items []model.DietRecordItem
	for _, record := range records {
		username := userMap[record.UserId]
		if username == "" {
			username = fmt.Sprintf("用户%d", record.UserId)
		}

		// 调试：打印原始数据
		g.Log().Debugf(ctx, "Record ID: %d, Date: %v, Time: %v", record.Id, record.Date, record.Time)

		// 格式化日期
		var dateStr string
		if record.Date != nil {
			// 尝试获取底层的time.Time
			stdTime := record.Date.Time
			dateStr = stdTime.Format("2006-01-02")
			g.Log().Debugf(ctx, "Record ID %d: gtime=%v, stdTime=%v, formatted=%s", record.Id, record.Date, stdTime, dateStr)
		} else {
			g.Log().Warningf(ctx, "Record ID %d has nil date", record.Id)
		}

		// 格式化时间
		var timeStr string
		if record.Time != nil {
			// 尝试获取底层的time.Time
			stdTime := record.Time.Time
			timeStr = stdTime.Format("15:04:05")
			g.Log().Debugf(ctx, "Record ID %d: gtime=%v, stdTime=%v, formatted=%s", record.Id, record.Time, stdTime, timeStr)
		} else {
			g.Log().Warningf(ctx, "Record ID %d has nil time", record.Id)
		}

		// 格式化创建时间
		var createdAt string
		if record.CreatedAt != nil {
			createdAt = record.CreatedAt.Format("2006-01-02 15:04:05")
		}

		item := model.DietRecordItem{
			Id:           record.Id,
			UserId:       record.UserId,
			Username:     username,
			Date:         dateStr,
			Time:         timeStr,
			MealType:     record.MealType,
			Remark:       record.Remark,
			TotalCalorie: record.TotalCalorie,
			Foods:        foodsMap[record.Id],
			CreatedAt:    createdAt,
		}

		if item.Foods == nil {
			item.Foods = []model.DietRecordFood{}
		}

		items = append(items, item)
	}

	return items, nil
}

// isValidMealType 验证餐次类型
func isValidMealType(mealType string) bool {
	validTypes := []string{"breakfast", "lunch", "dinner", "snacks"}
	for _, t := range validTypes {
		if t == mealType {
			return true
		}
	}
	return false
}

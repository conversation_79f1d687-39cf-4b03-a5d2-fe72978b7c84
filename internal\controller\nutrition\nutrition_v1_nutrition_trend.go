package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) NutritionTrend(ctx context.Context, req *v1.NutritionTrendReq) (res *v1.NutritionTrendRes, err error) {
	// 1. 从上下文获取当前用户信息
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.NutritionTrendInput{
		UserId:    user.UserId,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
	}

	// 3. 调用业务逻辑
	output, err := service.Nutrition().GetNutritionTrend(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.NutritionTrendRes{
		DateList:    output.Trend.DateList,
		CalorieList: output.Trend.CalorieList,
		ProteinList: output.Trend.ProteinList,
		CarbsList:   output.Trend.CarbsList,
		FatList:     output.Trend.FatList,
		Period:      req.Period,
		DataPoints:  len(output.Trend.DateList),
	}

	return res, nil
}

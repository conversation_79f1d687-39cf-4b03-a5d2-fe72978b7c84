package adminfood

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"

	"shikeyinxiang-goframe/api/adminfood/v1"
)

func (c *ControllerV1) CategoryCreate(ctx context.Context, req *v1.CategoryCreateReq) (res *v1.CategoryCreateRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.FoodCategoryCreateInput{
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		SortOrder:   req.SortOrder,
	}

	// 2. 调用业务逻辑
	output, err := service.FoodCategory().CreateCategory(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.CategoryCreateRes{
		Id:          output.CategoryInfo.Id,
		Name:        output.CategoryInfo.Name,
		Description: output.CategoryInfo.Description,
		Color:       output.CategoryInfo.Color,
		SortOrder:   output.CategoryInfo.SortOrder,
	}

	return res, nil
}

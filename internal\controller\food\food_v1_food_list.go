package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) FoodList(ctx context.Context, req *v1.FoodListReq) (res *v1.FoodListRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.FoodQueryInput{
		Page:       req.Page,
		Size:       req.Size,
		Keyword:    req.Keyword,
		CategoryId: req.CategoryId,
	}

	// 2. 调用业务逻辑
	output, err := service.Food().GetFoodList(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 转换食物列表格式
	var foodList []v1.FoodItem
	for _, food := range output.List {
		foodList = append(foodList, v1.FoodItem{
			Id:         food.Id,
			Name:       food.Name,
			Measure:    food.Measure,
			Grams:      food.Grams,
			Calories:   food.Calories,
			Protein:    food.Protein,
			Fat:        food.Fat,
			SatFat:     food.SatFat,
			Carbs:      food.Carbs,
			Category:   food.Category,
			CategoryId: food.CategoryId,
			ImageUrl:   food.ImageUrl,
			Unit:       "", // 根据需要设置
			Desc:       "", // 根据需要设置
			Remark:     "", // 根据需要设置
		})
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.FoodListRes{
		List:  foodList,
		Total: output.Total,
		Page:  output.Page,
		Size:  output.Size,
	}

	return res, nil
}

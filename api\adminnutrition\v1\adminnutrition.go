package v1

import "github.com/gogf/gf/v2/frame/g"

// 营养达标率请求（管理员功能）
type ComplianceRateReq struct {
	g.Meta `path:"/compliance-rate" tags:"Nutrition" method:"get" summary:"获取营养达标率"`
	Date   string `json:"date" v:"date" dc:"日期，格式：yyyy-MM-dd，默认当天"`
}

type ComplianceRateRes struct {
	Date           string  `json:"date" dc:"日期"`
	ComplianceRate float64 `json:"complianceRate" dc:"营养达标率（百分比，0-100）"`
	TotalUsers     int64   `json:"totalUsers" dc:"总用户数"`
	CompliantUsers int64   `json:"compliantUsers" dc:"达标用户数"`
}

// ==================== 管理员营养建议管理 ====================

// 获取所有营养建议请求
type AdminAdviceListReq struct {
	g.Meta `path:"/advice" tags:"Nutrition" method:"get" summary:"获取所有营养建议"`
}

type AdminAdviceListRes struct {
	List []NutritionAdviceItem `json:"list" dc:"营养建议列表"`
}

type NutritionAdviceItem struct {
	Id            int64  `json:"id" dc:"建议ID"`
	Type          string `json:"type" dc:"建议类型"`
	Title         string `json:"title" dc:"建议标题"`
	Description   string `json:"description" dc:"建议描述"`
	ConditionType string `json:"conditionType" dc:"条件类型"`
	MinPercentage *int   `json:"minPercentage" dc:"最小百分比"`
	MaxPercentage *int   `json:"maxPercentage" dc:"最大百分比"`
	IsDefault     bool   `json:"isDefault" dc:"是否默认建议"`
	Priority      int    `json:"priority" dc:"优先级"`
	Status        string `json:"status" dc:"状态"`
	CreatedAt     string `json:"createdAt" dc:"创建时间"`
	UpdatedAt     string `json:"updatedAt" dc:"更新时间"`
}

// 根据ID获取营养建议请求
type AdminAdviceDetailReq struct {
	g.Meta `path:"/advice/{id}" tags:"Nutrition" method:"get" summary:"根据ID获取营养建议"`
	Id     int64 `json:"id" v:"required" dc:"建议ID"`
}

type AdminAdviceDetailRes struct {
	Id            int64  `json:"id" dc:"建议ID"`
	Type          string `json:"type" dc:"建议类型"`
	Title         string `json:"title" dc:"建议标题"`
	Description   string `json:"description" dc:"建议描述"`
	ConditionType string `json:"conditionType" dc:"条件类型"`
	MinPercentage *int   `json:"minPercentage" dc:"最小百分比"`
	MaxPercentage *int   `json:"maxPercentage" dc:"最大百分比"`
	IsDefault     bool   `json:"isDefault" dc:"是否默认建议"`
	Priority      int    `json:"priority" dc:"优先级"`
	Status        string `json:"status" dc:"状态"`
	CreatedAt     string `json:"createdAt" dc:"创建时间"`
	UpdatedAt     string `json:"updatedAt" dc:"更新时间"`
}

// 创建营养建议请求
type AdminAdviceCreateReq struct {
	g.Meta        `path:"/advice" tags:"Nutrition" method:"post" summary:"创建营养建议"`
	Type          string `json:"type" v:"required" dc:"建议类型"`
	Title         string `json:"title" v:"required" dc:"建议标题"`
	Description   string `json:"description" v:"required" dc:"建议描述"`
	ConditionType string `json:"conditionType" v:"required" dc:"条件类型"`
	MinPercentage *int   `json:"minPercentage" dc:"最小百分比"`
	MaxPercentage *int   `json:"maxPercentage" dc:"最大百分比"`
	IsDefault     bool   `json:"isDefault" dc:"是否默认建议"`
	Priority      int    `json:"priority" dc:"优先级"`
	Status        string `json:"status" v:"required" dc:"状态"`
}

type AdminAdviceCreateRes struct {
	Id            int64  `json:"id" dc:"建议ID"`
	Type          string `json:"type" dc:"建议类型"`
	Title         string `json:"title" dc:"建议标题"`
	Description   string `json:"description" dc:"建议描述"`
	ConditionType string `json:"conditionType" dc:"条件类型"`
	MinPercentage *int   `json:"minPercentage" dc:"最小百分比"`
	MaxPercentage *int   `json:"maxPercentage" dc:"最大百分比"`
	IsDefault     bool   `json:"isDefault" dc:"是否默认建议"`
	Priority      int    `json:"priority" dc:"优先级"`
	Status        string `json:"status" dc:"状态"`
	CreatedAt     string `json:"createdAt" dc:"创建时间"`
	UpdatedAt     string `json:"updatedAt" dc:"更新时间"`
}

// 更新营养建议请求
type AdminAdviceUpdateReq struct {
	g.Meta        `path:"/advice/{id}" tags:"Nutrition" method:"put" summary:"更新营养建议"`
	Id            int64  `json:"id" v:"required" dc:"建议ID"`
	Type          string `json:"type" v:"required" dc:"建议类型"`
	Title         string `json:"title" v:"required" dc:"建议标题"`
	Description   string `json:"description" v:"required" dc:"建议描述"`
	ConditionType string `json:"conditionType" v:"required" dc:"条件类型"`
	MinPercentage *int   `json:"minPercentage" dc:"最小百分比"`
	MaxPercentage *int   `json:"maxPercentage" dc:"最大百分比"`
	IsDefault     bool   `json:"isDefault" dc:"是否默认建议"`
	Priority      int    `json:"priority" dc:"优先级"`
	Status        string `json:"status" v:"required" dc:"状态"`
}

type AdminAdviceUpdateRes struct {
	Id            int64  `json:"id" dc:"建议ID"`
	Type          string `json:"type" dc:"建议类型"`
	Title         string `json:"title" dc:"建议标题"`
	Description   string `json:"description" dc:"建议描述"`
	ConditionType string `json:"conditionType" dc:"条件类型"`
	MinPercentage *int   `json:"minPercentage" dc:"最小百分比"`
	MaxPercentage *int   `json:"maxPercentage" dc:"最大百分比"`
	IsDefault     bool   `json:"isDefault" dc:"是否默认建议"`
	Priority      int    `json:"priority" dc:"优先级"`
	Status        string `json:"status" dc:"状态"`
	CreatedAt     string `json:"createdAt" dc:"创建时间"`
	UpdatedAt     string `json:"updatedAt" dc:"更新时间"`
}

// 删除营养建议请求
type AdminAdviceDeleteReq struct {
	g.Meta `path:"/advice/{id}" tags:"Nutrition" method:"delete" summary:"删除营养建议"`
	Id     int64 `json:"id" v:"required" dc:"建议ID"`
}

type AdminAdviceDeleteRes struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// 根据条件类型获取营养建议请求
type AdminAdviceByConditionReq struct {
	g.Meta        `path:"/advice/condition/{conditionType}" tags:"Nutrition" method:"get" summary:"根据条件类型获取营养建议"`
	ConditionType string `json:"conditionType" v:"required" dc:"条件类型"`
}

type AdminAdviceByConditionRes struct {
	List []NutritionAdviceItem `json:"list" dc:"营养建议列表"`
}

// 管理员营养趋势请求
type AdminNutritionTrendReq struct {
	g.Meta `path:"/trend" tags:"Nutrition" method:"get" summary:"获取营养摄入趋势数据（管理员）"`
	Period string `json:"period" v:"in:week,month,year" dc:"时间周期" default:"month"`
}

type AdminNutritionTrendRes struct {
	TrendData map[string]interface{} `json:"trendData" dc:"趋势数据"`
}

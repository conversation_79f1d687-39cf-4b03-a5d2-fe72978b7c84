// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package adminnutrition

import (
	"context"

	"shikeyinxiang-goframe/api/adminnutrition/v1"
)

type IAdminnutritionV1 interface {
	ComplianceRate(ctx context.Context, req *v1.ComplianceRateReq) (res *v1.ComplianceRateRes, err error)
	AdminAdviceList(ctx context.Context, req *v1.AdminAdviceListReq) (res *v1.AdminAdviceListRes, err error)
	AdminAdviceDetail(ctx context.Context, req *v1.AdminAdviceDetailReq) (res *v1.AdminAdviceDetailRes, err error)
	AdminAdviceCreate(ctx context.Context, req *v1.AdminAdviceCreateReq) (res *v1.AdminAdviceCreateRes, err error)
	AdminAdviceUpdate(ctx context.Context, req *v1.AdminAdviceUpdateReq) (res *v1.AdminAdviceUpdateRes, err error)
	AdminAdviceDelete(ctx context.Context, req *v1.AdminAdviceDeleteReq) (res *v1.AdminAdviceDeleteRes, err error)
	AdminAdviceByCondition(ctx context.Context, req *v1.AdminAdviceByConditionReq) (res *v1.AdminAdviceByConditionRes, err error)
	AdminNutritionTrend(ctx context.Context, req *v1.AdminNutritionTrendReq) (res *v1.AdminNutritionTrendRes, err error)
}

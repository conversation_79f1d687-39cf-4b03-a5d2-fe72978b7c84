package admindiet

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"

	"shikeyinxiang-goframe/api/admindiet/v1"
)

func (c *ControllerV1) DietRecordDetail(ctx context.Context, req *v1.DietRecordDetailReq) (res *v1.DietRecordDetailRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.DietRecordDetailInput{
		RecordId: req.Id,
		UserId:   nil, // 普通用户查询，在service层会验证权限
	}

	// 2. 调用业务逻辑
	output, err := service.Diet().GetDietRecordDetail(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 转换食物列表格式
	var foods []v1.DietRecordFoodItem
	for _, food := range output.Record.Foods {
		foods = append(foods, v1.DietRecordFoodItem{
			FoodId:   food.FoodId,
			Name:     food.Name,
			Amount:   food.Amount,
			Unit:     food.Unit,
			Calories: food.Calories,
			Protein:  food.Protein,
			Fat:      food.Fat,
			Carbs:    food.Carbs,
			Grams:    food.Grams,
		})
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.DietRecordDetailRes{
		Id:           output.Record.Id,
		UserId:       output.Record.UserId,
		Username:     output.Record.Username,
		Date:         output.Record.Date,
		Time:         output.Record.Time,
		MealType:     output.Record.MealType,
		Remark:       output.Record.Remark,
		TotalCalorie: output.Record.TotalCalorie,
		Foods:        foods,
	}

	return res, nil
}

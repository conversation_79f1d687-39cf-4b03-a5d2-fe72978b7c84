package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) CategoryDelete(ctx context.Context, req *v1.CategoryDeleteReq) (res *v1.CategoryDeleteRes, err error) {
	// 1. 调用业务逻辑 - DeleteCategory方法只需要ID参数，直接传递
	err = service.FoodCategory().DeleteCategory(ctx, req.Id)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 2. 构造成功响应
	res = &v1.CategoryDeleteRes{
		Success: true,
		Message: "删除成功",
	}

	return res, nil
}

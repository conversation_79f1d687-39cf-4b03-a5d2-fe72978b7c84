package nutrition

import (
	"context"
	"time"

	"shikeyinxiang-goframe/api/nutrition/v1"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) HealthReport(ctx context.Context, req *v1.HealthReportReq) (res *v1.HealthReportRes, err error) {
	// 1. 从上下文获取当前用户信息
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 处理日期参数，如果为空则使用当天
	date := req.Date
	if date == "" {
		date = time.Now().Format("2006-01-02")
	}

	// 3. 获取当日营养统计
	dailyStatInput := &model.NutritionDailyStatInput{
		UserId: user.UserId,
		Date:   date,
	}

	dailyStatOutput, err := service.Nutrition().GetDailyNutritionStat(ctx, dailyStatInput)
	if err != nil {
		return nil, err
	}

	// 4. 获取营养建议
	adviceInput := &model.NutritionAdviceInput{
		UserId: user.UserId,
		Date:   date,
	}

	adviceOutput, err := service.Nutrition().GetNutritionAdvice(ctx, adviceInput)
	if err != nil {
		return nil, err
	}

	// 5. 转换营养建议格式
	var advices []v1.NutritionAdvice
	for _, advice := range adviceOutput.Advices {
		advices = append(advices, v1.NutritionAdvice{
			Type:        advice.Type,
			Title:       advice.Title,
			Description: advice.Description,
		})
	}

	// 6. 构造健康报告响应（简化版本，主要功能数据）
	res = &v1.HealthReportRes{
		Date: date,
		NutritionStat: v1.DailyNutritionRes{
			Date:              dailyStatOutput.Stat.Date,
			Calorie:           dailyStatOutput.Stat.Calorie,
			Protein:           dailyStatOutput.Stat.Protein,
			Carbs:             dailyStatOutput.Stat.Carbs,
			Fat:               dailyStatOutput.Stat.Fat,
			CaloriePercentage: dailyStatOutput.Stat.CaloriePercentage,
			ProteinPercentage: dailyStatOutput.Stat.ProteinPercentage,
			CarbsPercentage:   dailyStatOutput.Stat.CarbsPercentage,
			FatPercentage:     dailyStatOutput.Stat.FatPercentage,
		},
		Advices: advices,
		WeeklyProgress: v1.WeeklyProgress{
			Week:           1,
			AvgCalorie:     float64(dailyStatOutput.Stat.Calorie),
			AvgProtein:     dailyStatOutput.Stat.Protein,
			AvgCarbs:       dailyStatOutput.Stat.Carbs,
			AvgFat:         dailyStatOutput.Stat.Fat,
			ComplianceRate: (dailyStatOutput.Stat.CaloriePercentage + dailyStatOutput.Stat.ProteinPercentage + dailyStatOutput.Stat.CarbsPercentage + dailyStatOutput.Stat.FatPercentage) / 4,
		},
		Suggestion:  "基于当日营养摄入情况的建议",
		HealthScore: int((dailyStatOutput.Stat.CaloriePercentage + dailyStatOutput.Stat.ProteinPercentage + dailyStatOutput.Stat.CarbsPercentage + dailyStatOutput.Stat.FatPercentage) / 4),
		ScoreChange: 0, // 简化处理，实际需要对比历史数据
		NutritionBalance: v1.NutritionBalance{
			ProteinRatio: dailyStatOutput.Stat.ProteinPercentage / 100,
			CarbsRatio:   dailyStatOutput.Stat.CarbsPercentage / 100,
			FatRatio:     dailyStatOutput.Stat.FatPercentage / 100,
			IsBalanced:   dailyStatOutput.Stat.CaloriePercentage >= 80 && dailyStatOutput.Stat.CaloriePercentage <= 120,
		},
	}

	return res, nil
}

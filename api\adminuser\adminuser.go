// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package adminuser

import (
	"context"

	"shikeyinxiang-goframe/api/adminuser/v1"
)

type IAdminuserV1 interface {
	UserQuery(ctx context.Context, req *v1.UserQueryReq) (res *v1.UserQueryRes, err error)
	UserList(ctx context.Context, req *v1.UserListReq) (res *v1.UserListRes, err error)
	UserStatusUpdate(ctx context.Context, req *v1.UserStatusUpdateReq) (res *v1.UserStatusUpdateRes, err error)
	UserUpdate(ctx context.Context, req *v1.UserUpdateReq) (res *v1.UserUpdateRes, err error)
	ChangePassword(ctx context.Context, req *v1.ChangePasswordReq) (res *v1.ChangePasswordRes, err error)
	UserCreate(ctx context.Context, req *v1.UserCreateReq) (res *v1.UserCreateRes, err error)
}

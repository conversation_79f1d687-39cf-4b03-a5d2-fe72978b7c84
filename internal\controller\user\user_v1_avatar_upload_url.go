package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) AvatarUploadUrl(ctx context.Context, req *v1.AvatarUploadUrlReq) (res *v1.AvatarUploadUrlRes, err error) {
	// 1. 从上下文获取当前用户信息
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.AvatarUploadUrlInput{
		UserId:      user.UserId,
		ContentType: req.ContentType,
	}

	// 3. 调用业务逻辑
	output, err := service.User().GenerateAvatarUploadUrl(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.AvatarUploadUrlRes{
		UploadUrl: output.UploadUrl,
		FileName:  output.FileName,
		AvatarUrl: output.AvatarUrl,
	}

	return res, nil
}

package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) FoodImageUploadUrl(ctx context.Context, req *v1.FoodImageUploadUrlReq) (res *v1.FoodImageUploadUrlRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.FileUploadInput{
		UserId:      req.FoodId, // 使用FoodId作为UserId
		FileType:    "foodimage",
		ContentType: req.ContentType,
		Expiration:  30, // 30分钟有效期
	}

	// 2. 调用文件服务生成上传URL
	output, err := service.File().GenerateUploadUrl(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 生成访问URL（假设与上传URL相同的路径结构）
	imageUrl := output.FileName // 这里应该根据实际的文件访问规则生成

	// 4. 参数转换：业务层Output → API响应
	res = &v1.FoodImageUploadUrlRes{
		UploadUrl: output.UploadUrl,
		FileName:  output.FileName,
		ImageUrl:  imageUrl,
	}

	return res, nil
}

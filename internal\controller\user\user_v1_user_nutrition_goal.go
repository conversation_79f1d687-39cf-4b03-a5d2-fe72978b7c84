package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) UserNutritionGoal(ctx context.Context, req *v1.UserNutritionGoalReq) (res *v1.UserNutritionGoalRes, err error) {
	// 1. 从上下文获取当前用户信息
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.UserNutritionGoalQueryInput{
		UserId: user.UserId,
	}

	// 3. 调用业务逻辑
	output, err := service.User().GetUserNutritionGoal(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.UserNutritionGoalRes{
		Id:                output.Goal.UserId,
		UserId:            output.Goal.UserId,
		DailyCalorie:      float64(output.Goal.CalorieGoal),
		DailyProtein:      output.Goal.ProteinGoal,
		DailyCarbs:        output.Goal.CarbsGoal,
		DailyFat:          output.Goal.FatGoal,
		DailyFiber:        output.Goal.FiberGoal,
		DailySugar:        output.Goal.SugarGoal,
		DailySodium:       output.Goal.SodiumGoal,
		DailyPotassium:    output.Goal.PotassiumGoal,
		DailyCholesterol:  output.Goal.CholesterolGoal,
		DailyVitaminA:     output.Goal.VitaminAGoal,
		DailyVitaminC:     output.Goal.VitaminCGoal,
		DailyCalcium:      output.Goal.CalciumGoal,
		DailyIron:         output.Goal.IronGoal,
		CreateTime:        output.Goal.CreateTime.String(),
		UpdateTime:        output.Goal.UpdateTime.String(),
	}

	return res, nil
}

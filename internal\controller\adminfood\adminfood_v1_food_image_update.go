package adminfood

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"

	"shikeyinxiang-goframe/api/adminfood/v1"
)

func (c *ControllerV1) FoodImageUpdate(ctx context.Context, req *v1.FoodImageUpdateReq) (res *v1.FoodImageUpdateRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.FoodImageUpdateInput{
		Id:       req.Id,
		ImageUrl: req.ImageUrl,
	}

	// 2. 调用业务逻辑
	output, err := service.Food().UpdateFoodImage(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.FoodImageUpdateRes{
		Success: output.Success,
		Message: output.Message,
	}

	return res, nil
}

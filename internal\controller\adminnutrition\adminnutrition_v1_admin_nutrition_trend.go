package adminnutrition

import (
	"context"
	"shikeyinxiang-goframe/internal/service"

	"shikeyinxiang-goframe/api/adminnutrition/v1"
)

func (c *ControllerV1) AdminNutritionTrend(ctx context.Context, req *v1.AdminNutritionTrendReq) (res *v1.AdminNutritionTrendRes, err error) {
	// 1. 调用业务逻辑
	trendData, err := service.Nutrition().GetAdminNutritionTrend(ctx, req.Period)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 2. 参数转换：业务层Output → API响应
	res = &v1.AdminNutritionTrendRes{
		TrendData: trendData,
	}

	return res, nil
}

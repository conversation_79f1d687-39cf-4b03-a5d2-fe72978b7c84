package service

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
)

// IDashboard 仪表盘服务接口
type IDashboard interface {
	// GetDashboardStats 获取仪表盘统计数据
	GetDashboardStats(ctx context.Context, in *model.DashboardStatsInput) (*model.DashboardStatsOutput, error)

	// GetNutritionTrend 获取营养摄入趋势
	GetNutritionTrend(ctx context.Context, in *model.DashboardNutritionTrendInput) (*model.DashboardNutritionTrendOutput, error)

	// GetLatestDietRecords 获取最新饮食记录列表
	GetLatestDietRecords(ctx context.Context, in *model.DashboardLatestDietRecordsInput) (*model.DashboardLatestDietRecordsOutput, error)

	// GetDietRecordDetail 获取饮食记录详情
	GetDietRecordDetail(ctx context.Context, in *model.DashboardDietRecordDetailInput) (*model.DashboardDietRecordDetailOutput, error)

	// GetPopularFoods 获取热门食物统计
	GetPopularFoods(ctx context.Context, in *model.DashboardPopularFoodsInput) (*model.DashboardPopularFoodsOutput, error)
}

var (
	localDashboard IDashboard
)

// RegisterDashboard 注册仪表盘服务
func RegisterDashboard(i IDashboard) {
	localDashboard = i
}

// Dashboard 获取仪表盘服务
func Dashboard() IDashboard {
	if localDashboard == nil {
		panic("implement not found for interface IDashboard, forgot register?")
	}
	return localDashboard
}

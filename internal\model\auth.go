package model

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// UserLoginInput 用户登录输入参数
type UserLoginInput struct {
	Email    string `json:"email" v:"required|email" dc:"邮箱"`
	Password string `json:"password" v:"required" dc:"密码"`
}

// UserLoginOutput 用户登录输出结果
type UserLoginOutput struct {
	Token    string    `json:"token" dc:"JWT令牌"`
	UserInfo *UserInfo `json:"userInfo" dc:"用户信息"`
}

// AdminLoginInput 管理员登录输入参数
type AdminLoginInput struct {
	Username string `json:"username" v:"required" dc:"用户名"`
	Password string `json:"password" v:"required" dc:"密码"`
}

// AdminLoginOutput 管理员登录输出结果
type AdminLoginOutput struct {
	Token    string    `json:"token" dc:"JWT令牌"`
	UserInfo *UserInfo `json:"userInfo" dc:"用户信息"`
}

// WechatLoginInput 微信登录输入参数
type WechatLoginInput struct {
	Code          string `json:"code" v:"required" dc:"微信登录码"`
	EncryptedData string `json:"encryptedData" dc:"用户敏感数据"`
	Iv            string `json:"iv" dc:"偏移向量"`
}

// WechatLoginOutput 微信登录输出结果
type WechatLoginOutput struct {
	Token    string    `json:"token" dc:"JWT令牌"`
	UserInfo *UserInfo `json:"userInfo" dc:"用户信息"`
}

// LogoutInput 登出输入参数
type LogoutInput struct {
	Token string `json:"token" v:"required" dc:"JWT令牌"`
}

// LogoutOutput 登出输出结果
type LogoutOutput struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}



// RegisterInput 用户注册输入参数
type RegisterInput struct {
	Username  string `json:"username" v:"required|length:3,20" dc:"用户名"`
	Email     string `json:"email" v:"required|email" dc:"邮箱"`
	Password  string `json:"password" v:"required|min:6" dc:"密码"`
	Phone     string `json:"phone" dc:"手机号"`
	Role      string `json:"role" dc:"角色"`
	Status    int    `json:"status" dc:"状态"`
	AvatarUrl string `json:"avatarUrl" dc:"头像URL"`
	Openid    string `json:"openid" dc:"微信openid"`
}

// RegisterOutput 用户注册输出结果
type RegisterOutput struct {
	UserInfo *UserInfo `json:"userInfo" dc:"用户信息"`
}

// CurrentUserInput 获取当前用户信息输入参数
type CurrentUserInput struct {
	UserId int64 `json:"userId" v:"required" dc:"用户ID"`
}

// CurrentUserOutput 获取当前用户信息输出结果
type CurrentUserOutput struct {
	UserInfo *UserInfo `json:"userInfo" dc:"用户信息"`
}

// UserInfo 用户信息结构体
type UserInfo struct {
	Id         int64       `json:"id" dc:"用户ID"`
	Username   string      `json:"username" dc:"用户名"`
	Email      string      `json:"email" dc:"邮箱"`
	Phone      string      `json:"phone" dc:"手机号"`
	Role       string      `json:"role" dc:"角色"`
	Status     int         `json:"status" dc:"状态"`
	CreateTime *gtime.Time `json:"createTime" dc:"创建时间"`
	UpdateTime *gtime.Time `json:"updateTime" dc:"更新时间"`
	AvatarUrl  string      `json:"avatarUrl" dc:"头像URL"`
	Openid     string      `json:"openid" dc:"微信OpenID"`
	Password   string      `json:"-" dc:"密码（内部使用，不对外暴露）"`
}

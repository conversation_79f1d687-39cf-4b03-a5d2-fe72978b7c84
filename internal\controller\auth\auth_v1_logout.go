package auth

import (
	"context"

	"shikeyinxiang-goframe/api/auth/v1"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) Logout(ctx context.Context, req *v1.LogoutReq) (res *v1.LogoutRes, err error) {
	// 1. 从上下文获取当前用户token
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.LogoutInput{
		Token: user.JTI, // 使用JTI作为token标识
	}

	// 3. 调用业务逻辑
	output, err := service.Auth().Logout(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.LogoutRes{
		Message: output.Message,
	}

	return res, nil
}

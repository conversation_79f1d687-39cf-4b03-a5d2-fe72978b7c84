package dashboard

import (
	"context"

	"shikeyinxiang-goframe/api/dashboard/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) LatestDietRecords(ctx context.Context, req *v1.LatestDietRecordsReq) (res *v1.LatestDietRecordsRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.DashboardLatestDietRecordsInput{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		Page:      req.Page,
		Size:      req.Size,
	}

	// 2. 调用业务逻辑
	output, err := service.Dashboard().GetLatestDietRecords(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应 - 与Java项目PageResult保持一致
	res = &v1.LatestDietRecordsRes{
		Records: convertDietRecordItems(output.List),
		Total:   int64(output.Total),
		Current: output.Page,
		Size:    output.Size,
	}

	return res, nil
}

// convertDietRecordItems 转换饮食记录列表数据格式 - 与Java项目DietRecordResponseDTO保持一致
func convertDietRecordItems(items []model.DietRecordItem) []v1.DietRecordItem {
	result := make([]v1.DietRecordItem, len(items))
	for i, item := range items {
		// 直接使用字符串格式，与Java项目LocalDate和LocalTime的序列化格式保持一致

		// 转换食物列表
		foods := make([]v1.DietRecordFoodItem, len(item.Foods))
		for j, food := range item.Foods {
			foods[j] = v1.DietRecordFoodItem{
				FoodId:   food.FoodId,
				Name:     food.Name,
				Amount:   food.Amount,
				Unit:     food.Unit,
				Calories: food.Calories,
				Protein:  food.Protein,
				Fat:      food.Fat,
				Carbs:    food.Carbs,
				Grams:    food.Grams,
			}
		}

		result[i] = v1.DietRecordItem{
			Id:           item.Id,
			UserId:       item.UserId,
			Username:     item.Username,
			Date:         item.Date,     // 直接使用字符串，格式为 "2024-12-20"
			Time:         item.Time,     // 直接使用字符串，格式为 "12:30:00"
			MealType:     item.MealType,
			Remark:       item.Remark,
			TotalCalorie: item.TotalCalorie,
			Foods:        foods,
		}
	}
	return result
}

package dashboard

import (
	"context"

	"shikeyinxiang-goframe/api/dashboard/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) LatestDietRecords(ctx context.Context, req *v1.LatestDietRecordsReq) (res *v1.LatestDietRecordsRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.DashboardLatestDietRecordsInput{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		Page:      req.Page,
		Size:      req.Size,
	}

	// 2. 调用业务逻辑
	output, err := service.Dashboard().GetLatestDietRecords(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.LatestDietRecordsRes{
		List:  convertDietRecordItems(output.List),
		Total: int64(output.Total),
		Page:  output.Page,
		Size:  output.Size,
	}

	return res, nil
}

// convertDietRecordItems 转换饮食记录列表数据格式
func convertDietRecordItems(items []model.DietRecordItem) []v1.DietRecordItem {
	result := make([]v1.DietRecordItem, len(items))
	for i, item := range items {
		// 计算营养素总量
		var totalProtein, totalCarbs, totalFat float64
		for _, food := range item.Foods {
			totalProtein += food.Protein
			totalCarbs += food.Carbs
			totalFat += food.Fat
		}

		result[i] = v1.DietRecordItem{
			Id:           item.Id,
			UserId:       item.UserId,
			Username:     item.Username,
			MealType:     item.MealType,
			RecordDate:   nil, // 需要从Date字符串转换
			TotalCalorie: item.TotalCalorie,
			TotalProtein: totalProtein,
			TotalCarbs:   totalCarbs,
			TotalFat:     totalFat,
			CreatedAt:    nil, // model中没有此字段
		}
	}
	return result
}

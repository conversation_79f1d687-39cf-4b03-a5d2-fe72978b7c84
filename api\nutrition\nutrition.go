// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
)

type INutritionV1 interface {
	DailyNutrition(ctx context.Context, req *v1.DailyNutritionReq) (res *v1.DailyNutritionRes, err error)
	NutritionTrend(ctx context.Context, req *v1.NutritionTrendReq) (res *v1.NutritionTrendRes, err error)
	NutritionDetails(ctx context.Context, req *v1.NutritionDetailsReq) (res *v1.NutritionDetailsRes, err error)
	NutritionAdvice(ctx context.Context, req *v1.NutritionAdviceReq) (res *v1.NutritionAdviceRes, err error)
	HealthReport(ctx context.Context, req *v1.HealthReportReq) (res *v1.HealthReportRes, err error)
	ComplianceRate(ctx context.Context, req *v1.ComplianceRateReq) (res *v1.ComplianceRateRes, err error)
	AdminAdviceList(ctx context.Context, req *v1.AdminAdviceListReq) (res *v1.AdminAdviceListRes, err error)
	AdminAdviceDetail(ctx context.Context, req *v1.AdminAdviceDetailReq) (res *v1.AdminAdviceDetailRes, err error)
	AdminAdviceCreate(ctx context.Context, req *v1.AdminAdviceCreateReq) (res *v1.AdminAdviceCreateRes, err error)
	AdminAdviceUpdate(ctx context.Context, req *v1.AdminAdviceUpdateReq) (res *v1.AdminAdviceUpdateRes, err error)
	AdminAdviceDelete(ctx context.Context, req *v1.AdminAdviceDeleteReq) (res *v1.AdminAdviceDeleteRes, err error)
	AdminAdviceByCondition(ctx context.Context, req *v1.AdminAdviceByConditionReq) (res *v1.AdminAdviceByConditionRes, err error)
	AdminNutritionTrend(ctx context.Context, req *v1.AdminNutritionTrendReq) (res *v1.AdminNutritionTrendRes, err error)
}

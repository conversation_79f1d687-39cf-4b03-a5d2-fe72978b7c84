package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) UserNutritionGoalUpdate(ctx context.Context, req *v1.UserNutritionGoalUpdateReq) (res *v1.UserNutritionGoalUpdateRes, err error) {
	// 1. 从上下文获取当前用户信息
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.UserNutritionGoalUpdateInput{
		UserId:            user.UserId,
		DailyCalorie:      req.DailyCalorie,
		DailyProtein:      req.DailyProtein,
		DailyCarbs:        req.DailyCarbs,
		DailyFat:          req.DailyFat,
		DailyFiber:        req.DailyFiber,
		DailySugar:        req.DailySugar,
		DailySodium:       req.DailySodium,
		DailyPotassium:    req.DailyPotassium,
		DailyCholesterol:  req.DailyCholesterol,
		DailyVitaminA:     req.DailyVitaminA,
		DailyVitaminC:     req.DailyVitaminC,
		DailyCalcium:      req.DailyCalcium,
		DailyIron:         req.DailyIron,
	}

	// 3. 调用业务逻辑
	output, err := service.User().UpdateUserNutritionGoal(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.UserNutritionGoalUpdateRes{
		Success: output.Success,
		Message: output.Message,
	}

	return res, nil
}

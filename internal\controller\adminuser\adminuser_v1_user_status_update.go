package adminuser

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"

	"shikeyinxiang-goframe/api/adminuser/v1"
)

func (c *ControllerV1) UserStatusUpdate(ctx context.Context, req *v1.UserStatusUpdateReq) (res *v1.UserStatusUpdateRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.UserStatusUpdateInput{
		UserId: req.UserId,
		Status: req.Status,
	}

	// 2. 调用业务逻辑
	output, err := service.User().UpdateUserStatus(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.UserStatusUpdateRes{
		Success: output.Success,
		Message: output.Message,
	}

	return res, nil
}

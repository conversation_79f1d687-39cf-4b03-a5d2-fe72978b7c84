package adminuser

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"

	"shikeyinxiang-goframe/api/adminuser/v1"
)

func (c *ControllerV1) UserList(ctx context.Context, req *v1.UserListReq) (res *v1.UserListRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.UserQueryInput{
		Page:    req.Page,
		Size:    req.Size,
		Status:  req.Status,
		Keyword: req.Keyword,
		Role:    req.Role,
	}

	// 2. 调用业务逻辑
	output, err := service.User().GetUserList(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 转换用户信息列表
	var userList []v1.UserInfo
	for _, user := range output.List {
		userList = append(userList, v1.UserInfo{
			Id:         user.Id,
			Username:   user.Username,
			Email:      user.Email,
			Role:       user.Role,
			Status:     user.Status,
			CreateTime: user.CreateTime.String(),
			AvatarUrl:  user.AvatarUrl,
			Phone:      user.Phone,
		})
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.UserListRes{
		List:  userList,
		Total: output.Total,
		Page:  output.Page,
		Size:  output.Size,
	}

	return res, nil
}

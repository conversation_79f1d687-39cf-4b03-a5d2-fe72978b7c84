package middleware

import (
	"github.com/gogf/gf/v2/frame/g"
	"net/http"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/net/ghttp"
	"shikeyinxiang-goframe/internal/logic/errcode"
)

// sResponse 响应处理中间件
type sResponse struct{}

// ApiResponse 标准API响应格式，与Java项目保持一致
type ApiResponse struct {
	Code    int         `json:"code"`    // 错误码
	Message string      `json:"message"` // 错误信息
	Data    interface{} `json:"data"`    // 响应数据
}

// Handler 统一响应处理中间件
// 替换默认的ghttp.MiddlewareHandlerResponse，提供与Java项目兼容的ApiResponse格式
func (s *sResponse) Handler(r *ghttp.Request) {
	// 继续执行后续中间件和业务逻辑
	r.Middleware.Next()

	// 检查是否有错误发生
	if err := r.GetError(); err != nil {
		s.handleError(r, err)
		return
	}

	// 处理正常响应
	s.handleSuccess(r)
}

// handleError 处理错误响应
func (s *sResponse) handleError(r *ghttp.Request, err error) {
	// 清空响应缓冲区，避免重复输出
	r.Response.ClearBuffer()

	// 提取错误码和错误信息
	var (
		code    = gerror.Code(err)
		message = err.Error()
	)

	// 如果没有错误码，使用默认的服务器内部错误
	if code == nil {
		code = errcode.CodeServerError
	}

	// 记录错误日志
	g.Log().Error(r.Context(), "Request error:", g.Map{
		"url":     r.URL.String(),
		"method":  r.Method,
		"code":    code.Code(),
		"message": message,
		"error":   err,
	})

	// 构造标准错误响应
	response := ApiResponse{
		Code:    code.Code(),
		Message: message,
		Data:    nil,
	}

	// 根据错误码设置HTTP状态码
	httpStatus := s.getHttpStatusByCode(code.Code())
	r.Response.Status = httpStatus

	// 返回JSON格式的错误响应
	r.Response.WriteJson(response)
}

// handleSuccess 处理成功响应
func (s *sResponse) handleSuccess(r *ghttp.Request) {
	// 获取controller返回的数据
	responseData := r.GetHandlerResponse()

	// 如果没有返回数据，设置为nil
	if responseData == nil {
		responseData = nil
	}

	// 构造标准成功响应
	response := ApiResponse{
		Code:    200,          // 成功状态码为0
		Message: "success",    // 成功消息
		Data:    responseData, // controller返回的数据
	}

	// 清空响应缓冲区，避免重复输出
	r.Response.ClearBuffer()

	// 设置HTTP状态码为200
	r.Response.Status = http.StatusOK

	// 返回JSON格式的成功响应
	r.Response.WriteJson(response)
}

// getHttpStatusByCode 根据错误码获取对应的HTTP状态码
func (s *sResponse) getHttpStatusByCode(code int) int {
	switch {
	case code == 0:
		return http.StatusOK
	case code >= 400 && code < 500:
		// 4xx错误直接使用错误码作为HTTP状态码
		return code
	case code >= 500 && code < 600:
		// 5xx错误直接使用错误码作为HTTP状态码
		return code
	case code >= 10000 && code < 20000:
		// 认证模块错误，通常是401未授权
		return http.StatusUnauthorized
	case code >= 20000 && code < 30000:
		// 用户管理模块错误，通常是400错误请求
		return http.StatusBadRequest
	case code >= 30000 && code < 40000:
		// 食物管理模块错误，通常是400错误请求
		return http.StatusBadRequest
	case code >= 40000 && code < 50000:
		// 饮食记录模块错误，通常是400错误请求
		return http.StatusBadRequest
	case code >= 50000 && code < 60000:
		// 营养分析模块错误，通常是400错误请求
		return http.StatusBadRequest
	case code >= 60000 && code < 70000:
		// 文件管理模块错误，通常是400错误请求
		return http.StatusBadRequest
	case code >= 70000 && code < 80000:
		// 仪表盘模块错误，通常是400错误请求
		return http.StatusBadRequest
	default:
		// 其他错误默认为500内部服务器错误
		return http.StatusInternalServerError
	}
}

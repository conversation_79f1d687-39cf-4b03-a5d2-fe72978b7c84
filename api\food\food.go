// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
)

type IFoodV1 interface {
	FoodList(ctx context.Context, req *v1.FoodListReq) (res *v1.FoodListRes, err error)
	FoodDetail(ctx context.Context, req *v1.FoodDetailReq) (res *v1.FoodDetailRes, err error)
	CategoryList(ctx context.Context, req *v1.CategoryListReq) (res *v1.CategoryListRes, err error)
	CategoryDetail(ctx context.Context, req *v1.CategoryDetailReq) (res *v1.CategoryDetailRes, err error)
}

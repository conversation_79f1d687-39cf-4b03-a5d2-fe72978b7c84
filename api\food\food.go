// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
)

type IFoodV1 interface {
	FoodList(ctx context.Context, req *v1.FoodListReq) (res *v1.FoodListRes, err error)
	FoodDetail(ctx context.Context, req *v1.FoodDetailReq) (res *v1.FoodDetailRes, err error)
	FoodCreate(ctx context.Context, req *v1.FoodCreateReq) (res *v1.FoodCreateRes, err error)
	FoodUpdate(ctx context.Context, req *v1.FoodUpdateReq) (res *v1.FoodUpdateRes, err error)
	FoodDelete(ctx context.Context, req *v1.FoodDeleteReq) (res *v1.FoodDeleteRes, err error)
	CategoryList(ctx context.Context, req *v1.CategoryListReq) (res *v1.CategoryListRes, err error)
	CategoryCreate(ctx context.Context, req *v1.CategoryCreateReq) (res *v1.CategoryCreateRes, err error)
	CategoryUpdate(ctx context.Context, req *v1.CategoryUpdateReq) (res *v1.CategoryUpdateRes, err error)
	CategoryDelete(ctx context.Context, req *v1.CategoryDeleteReq) (res *v1.CategoryDeleteRes, err error)
	FoodImageUploadUrl(ctx context.Context, req *v1.FoodImageUploadUrlReq) (res *v1.FoodImageUploadUrlRes, err error)
	FoodImageUpdate(ctx context.Context, req *v1.FoodImageUpdateReq) (res *v1.FoodImageUpdateRes, err error)
	FoodBatchImport(ctx context.Context, req *v1.FoodBatchImportReq) (res *v1.FoodBatchImportRes, err error)
	CategoryDetail(ctx context.Context, req *v1.CategoryDetailReq) (res *v1.CategoryDetailRes, err error)
}

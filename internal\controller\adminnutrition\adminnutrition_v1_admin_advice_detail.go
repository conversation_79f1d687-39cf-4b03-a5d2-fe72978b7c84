package adminnutrition

import (
	"context"
	"shikeyinxiang-goframe/internal/service"

	"shikeyinxiang-goframe/api/adminnutrition/v1"
)

func (c *ControllerV1) AdminAdviceDetail(ctx context.Context, req *v1.AdminAdviceDetailReq) (res *v1.AdminAdviceDetailRes, err error) {
	// 1. 调用业务逻辑
	advice, err := service.Nutrition().GetAdviceByID(ctx, req.Id)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 2. 参数转换：业务层Output → API响应
	res = &v1.AdminAdviceDetailRes{
		Id:            advice.Id,
		Type:          advice.Type,
		Title:         advice.Title,
		Description:   advice.Description,
		ConditionType: advice.ConditionType,
		MinPercentage: advice.MinPercentage,
		MaxPercentage: advice.MaxPercentage,
		IsDefault:     advice.IsDefault,
		Priority:      advice.Priority,
		Status:        advice.Status,
		CreatedAt:     advice.CreatedAt.String(),
		UpdatedAt:     advice.UpdatedAt.String(),
	}

	return res, nil
}

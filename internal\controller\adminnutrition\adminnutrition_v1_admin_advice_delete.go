package adminnutrition

import (
	"context"
	"shikeyinxiang-goframe/internal/service"

	"shikeyinxiang-goframe/api/adminnutrition/v1"
)

func (c *ControllerV1) AdminAdviceDelete(ctx context.Context, req *v1.AdminAdviceDeleteReq) (res *v1.AdminAdviceDeleteRes, err error) {
	// 1. 调用业务逻辑
	err = service.Nutrition().DeleteAdvice(ctx, req.Id)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 2. 参数转换：业务层Output → API响应
	res = &v1.AdminAdviceDeleteRes{
		Success: true,
		Message: "删除成功",
	}

	return res, nil
}

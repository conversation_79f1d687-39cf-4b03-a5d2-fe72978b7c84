package model

// ==================== 饮食记录相关结构体 ====================

// DietRecordFood 饮食记录食物项
type DietRecordFood struct {
	FoodId   int64   `json:"foodId" dc:"食物ID"`
	Name     string  `json:"name" dc:"食物名称"`
	Amount   float64 `json:"amount" dc:"食用量"`
	Unit     string  `json:"unit" dc:"单位"`
	Calories float64 `json:"calories" dc:"卡路里"`
	Protein  float64 `json:"protein" dc:"蛋白质(g)"`
	Fat      float64 `json:"fat" dc:"脂肪(g)"`
	Carbs    float64 `json:"carbs" dc:"碳水(g)"`
	Grams    float64 `json:"grams" dc:"克数"`
}

// DietRecordItem 饮食记录项
type DietRecordItem struct {
	Id           int64            `json:"id" dc:"记录ID"`
	UserId       int64            `json:"userId" dc:"用户ID"`
	Username     string           `json:"username" dc:"用户名"`
	Date         string           `json:"date" dc:"记录日期"`
	Time         string           `json:"time" dc:"记录时间"`
	MealType     string           `json:"mealType" dc:"餐次类型"`
	Remark       string           `json:"remark" dc:"备注信息"`
	TotalCalorie float64          `json:"totalCalorie" dc:"总热量(千卡)"`
	Foods        []DietRecordFood `json:"foods" dc:"食物列表"`
	CreatedAt    string           `json:"createdAt" dc:"创建时间"`
}

// ==================== 添加饮食记录 ====================

// DietRecordCreateInput 添加饮食记录输入
type DietRecordCreateInput struct {
	UserId       int64            `json:"userId" dc:"用户ID"`
	Date         string           `json:"date" dc:"记录日期"`
	Time         string           `json:"time" dc:"记录时间"`
	MealType     string           `json:"mealType" dc:"餐次类型"`
	Remark       string           `json:"remark" dc:"备注信息"`
	TotalCalorie float64          `json:"totalCalorie" dc:"总热量(千卡)"`
	Foods        []DietRecordFood `json:"foods" dc:"食物列表"`
}

// DietRecordCreateOutput 添加饮食记录输出
type DietRecordCreateOutput struct {
	RecordId int64 `json:"recordId" dc:"记录ID"`
}

// ==================== 查询饮食记录 ====================

// DietRecordQueryInput 查询饮食记录输入
type DietRecordQueryInput struct {
	UserId    int64  `json:"userId" dc:"用户ID"`
	StartDate string `json:"startDate" dc:"开始日期"`
	EndDate   string `json:"endDate" dc:"结束日期"`
	MealType  string `json:"mealType" dc:"餐次类型"`
	Page      int    `json:"page" dc:"当前页"`
	Size      int    `json:"size" dc:"每页大小"`
}

// DietRecordQueryOutput 查询饮食记录输出
type DietRecordQueryOutput struct {
	List  []DietRecordItem `json:"list" dc:"记录列表"`
	Page  int              `json:"page" dc:"当前页"`
	Size  int              `json:"size" dc:"每页大小"`
	Total int              `json:"total" dc:"总数"`
}

// ==================== 管理员查询所有用户饮食记录 ====================

// DietRecordAdminQueryInput 管理员查询所有用户饮食记录输入
type DietRecordAdminQueryInput struct {
	UserId    *int64 `json:"userId" dc:"用户ID(可选)"`
	StartDate string `json:"startDate" dc:"开始日期"`
	EndDate   string `json:"endDate" dc:"结束日期"`
	MealType  string `json:"mealType" dc:"餐次类型"`
	Page      int    `json:"page" dc:"当前页"`
	Size      int    `json:"size" dc:"每页大小"`
}

// DietRecordAdminQueryOutput 管理员查询所有用户饮食记录输出
type DietRecordAdminQueryOutput struct {
	List  []DietRecordItem `json:"list" dc:"记录列表"`
	Page  int              `json:"page" dc:"当前页"`
	Size  int              `json:"size" dc:"每页大小"`
	Total int              `json:"total" dc:"总数"`
}

// ==================== 获取饮食记录详情 ====================

// DietRecordDetailInput 获取饮食记录详情输入
type DietRecordDetailInput struct {
	RecordId int64  `json:"recordId" dc:"记录ID"`
	UserId   *int64 `json:"userId" dc:"用户ID(可选，管理员查询时不需要)"`
}

// DietRecordDetailOutput 获取饮食记录详情输出
type DietRecordDetailOutput struct {
	Record DietRecordItem `json:"record" dc:"饮食记录详情"`
}

// ==================== 删除饮食记录 ====================

// DietRecordDeleteInput 删除饮食记录输入
type DietRecordDeleteInput struct {
	RecordId int64  `json:"recordId" dc:"记录ID"`
	UserId   *int64 `json:"userId" dc:"用户ID(可选，管理员删除时不需要)"`
}

// DietRecordDeleteOutput 删除饮食记录输出
type DietRecordDeleteOutput struct {
	Success bool `json:"success" dc:"删除是否成功"`
}

// ==================== 统计相关 ====================

// DietRecordCountByDateInput 统计指定日期记录数量输入
type DietRecordCountByDateInput struct {
	Date string `json:"date" dc:"日期"`
}

// DietRecordCountByDateOutput 统计指定日期记录数量输出
type DietRecordCountByDateOutput struct {
	Count int `json:"count" dc:"记录数量"`
}

// DietRecordActiveUsersInput 获取活跃用户ID列表输入
type DietRecordActiveUsersInput struct {
	Date      *string `json:"date" dc:"指定日期(可选)"`
	StartDate *string `json:"startDate" dc:"开始日期(可选)"`
	EndDate   *string `json:"endDate" dc:"结束日期(可选)"`
}

// DietRecordActiveUsersOutput 获取活跃用户ID列表输出
type DietRecordActiveUsersOutput struct {
	UserIds []int64 `json:"userIds" dc:"活跃用户ID列表"`
}

// DietRecordPopularFoodsInput 获取热门食物统计输入
type DietRecordPopularFoodsInput struct {
	Period string `json:"period" dc:"时间周期: week/month/quarter"`
	Limit  int    `json:"limit" dc:"返回数量限制"`
}

// DietRecordPopularFoodsOutput 获取热门食物统计输出
type DietRecordPopularFoodsOutput struct {
	Foods []PopularFoodItem `json:"foods" dc:"热门食物列表"`
}

// PopularFoodItem 热门食物项
type PopularFoodItem struct {
	FoodName string `json:"foodName" dc:"食物名称"`
	Count    int    `json:"count" dc:"使用次数"`
}

// ==================== 批量查询(供营养统计使用) ====================

// DietRecordBatchQueryInput 批量查询饮食记录输入
type DietRecordBatchQueryInput struct {
	UserIds   []int64 `json:"userIds" dc:"用户ID列表"`
	StartDate string  `json:"startDate" dc:"开始日期"`
	EndDate   string  `json:"endDate" dc:"结束日期"`
}

// DietRecordBatchQueryOutput 批量查询饮食记录输出
type DietRecordBatchQueryOutput struct {
	// 按用户ID和日期分组的饮食记录Map，格式：Map<userId, Map<date, Records<DietRecordItem>>>
	Records map[int64]map[string][]DietRecordItem `json:"records" dc:"按用户ID和日期分组的饮食记录"`
}

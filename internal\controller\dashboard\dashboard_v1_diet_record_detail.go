package dashboard

import (
	"context"

	"shikeyinxiang-goframe/api/dashboard/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) DietRecordDetail(ctx context.Context, req *v1.DietRecordDetailReq) (res *v1.DietRecordDetailRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.DashboardDietRecordDetailInput{
		RecordId: req.RecordId,
	}

	// 2. 调用业务逻辑
	output, err := service.Dashboard().GetDietRecordDetail(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	// 注意：model.DietRecordItem与API响应结构不完全匹配，需要转换
	res = &v1.DietRecordDetailRes{
		Id:           output.Record.Id,
		UserId:       output.Record.UserId,
		Username:     output.Record.Username,
		MealType:     output.Record.MealType,
		RecordDate:   nil, // model中是Date字符串，需要转换
		TotalCalorie: output.Record.TotalCalorie,
		TotalProtein: 0,   // model中没有此字段，需要从Foods计算
		TotalCarbs:   0,   // model中没有此字段，需要从Foods计算
		TotalFat:     0,   // model中没有此字段，需要从Foods计算
		Foods:        convertDietRecordFoods(output.Record.Foods),
		CreatedAt:    nil, // model中没有此字段
		UpdatedAt:    nil, // model中没有此字段
	}

	return res, nil
}

// convertDietRecordFoods 转换饮食记录食物数据格式
func convertDietRecordFoods(foods []model.DietRecordFood) []v1.DietRecordFood {
	result := make([]v1.DietRecordFood, len(foods))
	for i, food := range foods {
		result[i] = v1.DietRecordFood{
			FoodId:   food.FoodId,
			FoodName: food.Name,
			Quantity: food.Amount,
			Unit:     food.Unit,
			Calorie:  food.Calories,
			Protein:  food.Protein,
			Carbs:    food.Carbs,
			Fat:      food.Fat,
		}
	}
	return result
}

package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) NutritionDetails(ctx context.Context, req *v1.NutritionDetailsReq) (res *v1.NutritionDetailsRes, err error) {
	// 1. 从上下文获取当前用户信息
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.NutritionDetailsInput{
		UserId: user.UserId,
		Date:   req.Date,
	}

	// 3. 调用业务逻辑
	output, err := service.Nutrition().GetNutritionDetails(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 转换详情格式
	var details []v1.NutritionDetailItem
	for _, detail := range output.Details {
		details = append(details, v1.NutritionDetailItem{
			Name:       detail.Name,
			Value:      detail.Value,
			Unit:       detail.Unit,
			Percentage: detail.Percentage,
		})
	}

	// 5. 参数转换：业务层Output → API响应
	res = &v1.NutritionDetailsRes{
		Details: details,
	}

	return res, nil
}

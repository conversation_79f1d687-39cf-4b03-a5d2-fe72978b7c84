package adminuser

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"

	"shikeyinxiang-goframe/api/adminuser/v1"
)

func (c *ControllerV1) UserUpdate(ctx context.Context, req *v1.UserUpdateReq) (res *v1.UserUpdateRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.UserUpdateInput{
		Id:        req.UserId, // 使用Id字段而不是UserId
		Username:  req.Username,
		Email:     req.Email,
		Role:      req.Role,
		Status:    req.Status,
		AvatarUrl: req.AvatarUrl,
	}

	// 2. 调用业务逻辑
	_, err = service.User().UpdateUser(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 获取更新后的用户信息
	userInfo, err := service.User().GetUserByID(ctx, req.UserId)
	if err != nil {
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.UserUpdateRes{
		Id:         userInfo.Id,
		Username:   userInfo.Username,
		Email:      userInfo.Email,
		Role:       userInfo.Role,
		Status:     userInfo.Status,
		CreateTime: userInfo.CreateTime.String(),
		AvatarUrl:  userInfo.AvatarUrl,
	}

	return res, nil
}

package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 食物列表查询请求
type FoodListReq struct {
	g.Meta     `path:"/list" tags:"Food" method:"get" summary:"获取食物列表"`
	Page       int    `json:"page" v:"min:1" dc:"页码" default:"1"`
	Size       int    `json:"size" v:"between:1,100" dc:"每页数量" default:"10"`
	Keyword    string `json:"keyword" dc:"搜索关键词"`
	CategoryId *int   `json:"categoryId" dc:"分类ID"`
}

type FoodListRes struct {
	List  []FoodItem `json:"list" dc:"食物列表"`
	Total int64      `json:"total" dc:"总数"`
	Page  int        `json:"page" dc:"当前页"`
	Size  int        `json:"size" dc:"每页数量"`
}

type FoodItem struct {
	Id         int     `json:"id" dc:"食物ID"`
	Name       string  `json:"name" dc:"食物名称"`
	Measure    string  `json:"measure" dc:"份量描述"`
	Grams      float64 `json:"grams" dc:"克数"`
	Calories   float64 `json:"calories" dc:"卡路里"`
	Protein    float64 `json:"protein" dc:"蛋白质(g)"`
	Fat        float64 `json:"fat" dc:"脂肪(g)"`
	SatFat     float64 `json:"satFat" dc:"饱和脂肪(g)"`
	Carbs      float64 `json:"carbs" dc:"碳水(g)"`
	Category   string  `json:"category" dc:"分类名称"`
	CategoryId int     `json:"categoryId" dc:"分类ID"`
	ImageUrl   string  `json:"imageUrl" dc:"图片URL"`
	Unit       string  `json:"unit" dc:"单位"`
	Desc       string  `json:"desc" dc:"描述信息"`
	Remark     string  `json:"remark" dc:"备注"`
}

// 食物详情查询请求
type FoodDetailReq struct {
	g.Meta `path:"/{id}" tags:"Food" method:"get" summary:"获取食物详情"`
	Id     int `json:"id" v:"required" dc:"食物ID"`
}

type FoodDetailRes struct {
	Id         int     `json:"id" dc:"食物ID"`
	Name       string  `json:"name" dc:"食物名称"`
	Measure    string  `json:"measure" dc:"份量描述"`
	Grams      float64 `json:"grams" dc:"克数"`
	Calories   float64 `json:"calories" dc:"卡路里"`
	Protein    float64 `json:"protein" dc:"蛋白质(g)"`
	Fat        float64 `json:"fat" dc:"脂肪(g)"`
	SatFat     float64 `json:"satFat" dc:"饱和脂肪(g)"`
	Carbs      float64 `json:"carbs" dc:"碳水(g)"`
	Category   string  `json:"category" dc:"分类名称"`
	CategoryId int     `json:"categoryId" dc:"分类ID"`
	ImageUrl   string  `json:"imageUrl" dc:"图片URL"`
	Unit       string  `json:"unit" dc:"单位"`
	Desc       string  `json:"desc" dc:"描述信息"`
	Remark     string  `json:"remark" dc:"备注"`
}

// 食物分类列表请求（支持分页和全量查询）
type CategoryListReq struct {
	g.Meta `path:"/category" tags:"Food" method:"get" summary:"获取食物分类列表"`
	Page   *int `json:"page" v:"min:1" dc:"页码，为空时返回全部数据"`
	Size   *int `json:"size" v:"between:1,100" dc:"每页数量，为空时返回全部数据"`
}

type CategoryListRes struct {
	List  []FoodCategory `json:"list" dc:"分类列表"`
	Total int64          `json:"total" dc:"总数"`
	Page  *int           `json:"page" dc:"当前页"`
	Size  *int           `json:"size" dc:"每页数量"`
}

type FoodCategory struct {
	Id          int    `json:"id" dc:"分类ID"`
	Name        string `json:"name" dc:"分类名称"`
	Description string `json:"description" dc:"分类描述"`
	Color       string `json:"color" dc:"分类颜色"`
	SortOrder   int    `json:"sortOrder" dc:"排序顺序"`
	FoodCount   int    `json:"foodCount" dc:"该分类下的食物数量"`
}

// 分类详情查询请求
type CategoryDetailReq struct {
	g.Meta `path:"/category/{id}" tags:"Food" method:"get" summary:"获取食物分类详情"`
	Id     int `json:"id" v:"required" dc:"分类ID"`
}

type CategoryDetailRes struct {
	Id          int    `json:"id" dc:"分类ID"`
	Name        string `json:"name" dc:"分类名称"`
	Description string `json:"description" dc:"分类描述"`
	Color       string `json:"color" dc:"分类颜色"`
	SortOrder   int    `json:"sortOrder" dc:"排序顺序"`
	FoodCount   int    `json:"foodCount" dc:"该分类下的食物数量"`
}

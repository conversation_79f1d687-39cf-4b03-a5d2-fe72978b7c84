package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 食物列表查询请求
type FoodListReq struct {
	g.Meta     `path:"/list" tags:"Food" method:"get" summary:"获取食物列表"`
	Page       int    `json:"page" v:"min:1" dc:"页码" default:"1"`
	Size       int    `json:"size" v:"between:1,100" dc:"每页数量" default:"10"`
	Keyword    string `json:"keyword" dc:"搜索关键词"`
	CategoryId *int   `json:"categoryId" dc:"分类ID"`
}

type FoodListRes struct {
	List  []FoodItem `json:"list" dc:"食物列表"`
	Total int64      `json:"total" dc:"总数"`
	Page  int        `json:"page" dc:"当前页"`
	Size  int        `json:"size" dc:"每页数量"`
}

type FoodItem struct {
	Id         int     `json:"id" dc:"食物ID"`
	Name       string  `json:"name" dc:"食物名称"`
	Measure    string  `json:"measure" dc:"份量描述"`
	Grams      float64 `json:"grams" dc:"克数"`
	Calories   float64 `json:"calories" dc:"卡路里"`
	Protein    float64 `json:"protein" dc:"蛋白质(g)"`
	Fat        float64 `json:"fat" dc:"脂肪(g)"`
	SatFat     float64 `json:"satFat" dc:"饱和脂肪(g)"`
	Carbs      float64 `json:"carbs" dc:"碳水(g)"`
	Category   string  `json:"category" dc:"分类名称"`
	CategoryId int     `json:"categoryId" dc:"分类ID"`
	ImageUrl   string  `json:"imageUrl" dc:"图片URL"`
	Unit       string  `json:"unit" dc:"单位"`
	Desc       string  `json:"desc" dc:"描述信息"`
	Remark     string  `json:"remark" dc:"备注"`
}

// 食物详情查询请求
type FoodDetailReq struct {
	g.Meta `path:"/{id}" tags:"Food" method:"get" summary:"获取食物详情"`
	Id     int `json:"id" v:"required" dc:"食物ID"`
}

type FoodDetailRes struct {
	Id         int     `json:"id" dc:"食物ID"`
	Name       string  `json:"name" dc:"食物名称"`
	Measure    string  `json:"measure" dc:"份量描述"`
	Grams      float64 `json:"grams" dc:"克数"`
	Calories   float64 `json:"calories" dc:"卡路里"`
	Protein    float64 `json:"protein" dc:"蛋白质(g)"`
	Fat        float64 `json:"fat" dc:"脂肪(g)"`
	SatFat     float64 `json:"satFat" dc:"饱和脂肪(g)"`
	Carbs      float64 `json:"carbs" dc:"碳水(g)"`
	Category   string  `json:"category" dc:"分类名称"`
	CategoryId int     `json:"categoryId" dc:"分类ID"`
	ImageUrl   string  `json:"imageUrl" dc:"图片URL"`
	Unit       string  `json:"unit" dc:"单位"`
	Desc       string  `json:"desc" dc:"描述信息"`
	Remark     string  `json:"remark" dc:"备注"`
}

// 食物创建请求
type FoodCreateReq struct {
	g.Meta     `path:"/" tags:"Food" method:"post" summary:"创建食物"`
	Name       string  `json:"name" v:"required" dc:"食物名称"`
	Measure    string  `json:"measure" v:"required" dc:"份量描述"`
	Grams      float64 `json:"grams" v:"required|min:0" dc:"克数"`
	Calories   float64 `json:"calories" v:"required|min:0" dc:"卡路里"`
	Protein    float64 `json:"protein" v:"required|min:0" dc:"蛋白质(g)"`
	Fat        float64 `json:"fat" v:"required|min:0" dc:"脂肪(g)"`
	SatFat     float64 `json:"satFat" v:"min:0" dc:"饱和脂肪(g)"`
	Carbs      float64 `json:"carbs" v:"required|min:0" dc:"碳水(g)"`
	CategoryId int     `json:"categoryId" v:"required" dc:"分类ID"`
	ImageUrl   string  `json:"imageUrl" dc:"图片URL"`
	Unit       string  `json:"unit" dc:"单位"`
	Desc       string  `json:"desc" dc:"描述信息"`
	Remark     string  `json:"remark" dc:"备注"`
}

type FoodCreateRes struct {
	Id         int     `json:"id" dc:"食物ID"`
	Name       string  `json:"name" dc:"食物名称"`
	Measure    string  `json:"measure" dc:"份量描述"`
	Grams      float64 `json:"grams" dc:"克数"`
	Calories   float64 `json:"calories" dc:"卡路里"`
	Protein    float64 `json:"protein" dc:"蛋白质(g)"`
	Fat        float64 `json:"fat" dc:"脂肪(g)"`
	SatFat     float64 `json:"satFat" dc:"饱和脂肪(g)"`
	Carbs      float64 `json:"carbs" dc:"碳水(g)"`
	Category   string  `json:"category" dc:"分类名称"`
	CategoryId int     `json:"categoryId" dc:"分类ID"`
	ImageUrl   string  `json:"imageUrl" dc:"图片URL"`
	Unit       string  `json:"unit" dc:"单位"`
	Desc       string  `json:"desc" dc:"描述信息"`
	Remark     string  `json:"remark" dc:"备注"`
}

// 食物更新请求
type FoodUpdateReq struct {
	g.Meta     `path:"/{id}" tags:"Food" method:"put" summary:"更新食物信息"`
	Id         int     `json:"id" v:"required" dc:"食物ID"`
	Name       string  `json:"name" dc:"食物名称"`
	Measure    string  `json:"measure" dc:"份量描述"`
	Grams      float64 `json:"grams" v:"min:0" dc:"克数"`
	Calories   float64 `json:"calories" v:"min:0" dc:"卡路里"`
	Protein    float64 `json:"protein" v:"min:0" dc:"蛋白质(g)"`
	Fat        float64 `json:"fat" v:"min:0" dc:"脂肪(g)"`
	SatFat     float64 `json:"satFat" v:"min:0" dc:"饱和脂肪(g)"`
	Carbs      float64 `json:"carbs" v:"min:0" dc:"碳水(g)"`
	CategoryId int     `json:"categoryId" dc:"分类ID"`
	ImageUrl   string  `json:"imageUrl" dc:"图片URL"`
	Unit       string  `json:"unit" dc:"单位"`
	Desc       string  `json:"desc" dc:"描述信息"`
	Remark     string  `json:"remark" dc:"备注"`
}

type FoodUpdateRes struct {
	Id         int     `json:"id" dc:"食物ID"`
	Name       string  `json:"name" dc:"食物名称"`
	Measure    string  `json:"measure" dc:"份量描述"`
	Grams      float64 `json:"grams" dc:"克数"`
	Calories   float64 `json:"calories" dc:"卡路里"`
	Protein    float64 `json:"protein" dc:"蛋白质(g)"`
	Fat        float64 `json:"fat" dc:"脂肪(g)"`
	SatFat     float64 `json:"satFat" dc:"饱和脂肪(g)"`
	Carbs      float64 `json:"carbs" dc:"碳水(g)"`
	Category   string  `json:"category" dc:"分类名称"`
	CategoryId int     `json:"categoryId" dc:"分类ID"`
	ImageUrl   string  `json:"imageUrl" dc:"图片URL"`
	Unit       string  `json:"unit" dc:"单位"`
	Desc       string  `json:"desc" dc:"描述信息"`
	Remark     string  `json:"remark" dc:"备注"`
}

// 食物删除请求
type FoodDeleteReq struct {
	g.Meta `path:"/{id}" tags:"Food" method:"delete" summary:"删除食物"`
	Id     int `json:"id" v:"required" dc:"食物ID"`
}

type FoodDeleteRes struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// 食物分类列表请求（支持分页和全量查询）
type CategoryListReq struct {
	g.Meta `path:"/category" tags:"Food" method:"get" summary:"获取食物分类列表"`
	Page   *int `json:"page" v:"min:1" dc:"页码，为空时返回全部数据"`
	Size   *int `json:"size" v:"between:1,100" dc:"每页数量，为空时返回全部数据"`
}

type CategoryListRes struct {
	List  []FoodCategory `json:"list" dc:"分类列表"`
	Total int64          `json:"total" dc:"总数"`
	Page  *int           `json:"page" dc:"当前页"`
	Size  *int           `json:"size" dc:"每页数量"`
}

type FoodCategory struct {
	Id          int    `json:"id" dc:"分类ID"`
	Name        string `json:"name" dc:"分类名称"`
	Description string `json:"description" dc:"分类描述"`
	Color       string `json:"color" dc:"分类颜色"`
	SortOrder   int    `json:"sortOrder" dc:"排序顺序"`
	FoodCount   int    `json:"foodCount" dc:"该分类下的食物数量"`
}

// 分类创建请求
type CategoryCreateReq struct {
	g.Meta      `path:"/category" tags:"Food" method:"post" summary:"创建食物分类"`
	Name        string `json:"name" v:"required" dc:"分类名称"`
	Description string `json:"description" dc:"分类描述"`
	Color       string `json:"color" dc:"分类颜色"`
	SortOrder   int    `json:"sortOrder" dc:"排序顺序"`
}

type CategoryCreateRes struct {
	Id          int    `json:"id" dc:"分类ID"`
	Name        string `json:"name" dc:"分类名称"`
	Description string `json:"description" dc:"分类描述"`
	Color       string `json:"color" dc:"分类颜色"`
	SortOrder   int    `json:"sortOrder" dc:"排序顺序"`
}

// 分类更新请求
type CategoryUpdateReq struct {
	g.Meta      `path:"/category/{id}" tags:"Food" method:"put" summary:"更新食物分类"`
	Id          int    `json:"id" v:"required" dc:"分类ID"`
	Name        string `json:"name" dc:"分类名称"`
	Description string `json:"description" dc:"分类描述"`
	Color       string `json:"color" dc:"分类颜色"`
	SortOrder   int    `json:"sortOrder" dc:"排序顺序"`
}

type CategoryUpdateRes struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// 分类删除请求
type CategoryDeleteReq struct {
	g.Meta `path:"/category/{id}" tags:"Food" method:"delete" summary:"删除食物分类"`
	Id     int `json:"id" v:"required" dc:"分类ID"`
}

type CategoryDeleteRes struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// 食物图片上传URL请求
type FoodImageUploadUrlReq struct {
	g.Meta      `path:"/upload-image-url" tags:"Food" method:"get" summary:"获取食物图片上传URL"`
	FoodId      int64  `json:"foodId" v:"required" dc:"食物ID"`
	ContentType string `json:"contentType" v:"required" dc:"文件内容类型"`
}

type FoodImageUploadUrlRes struct {
	UploadUrl string `json:"uploadUrl" dc:"上传预签名URL"`
	FileName  string `json:"fileName" dc:"生成的文件名"`
	ImageUrl  string `json:"imageUrl" dc:"图片访问URL"`
}

// 食物图片更新请求
type FoodImageUpdateReq struct {
	g.Meta   `path:"/{id}/image" tags:"Food" method:"put" summary:"更新食物图片URL"`
	Id       int    `json:"id" v:"required" dc:"食物ID"`
	ImageUrl string `json:"imageUrl" v:"required" dc:"图片URL"`
}

type FoodImageUpdateRes struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// 批量导入食物请求
type FoodBatchImportReq struct {
	g.Meta `path:"/import" tags:"Food" method:"post" summary:"批量导入食物"`
	Foods  []FoodImportItem `json:"foods" v:"required" dc:"食物列表"`
}

type FoodImportItem struct {
	Name       string  `json:"name" v:"required" dc:"食物名称"`
	Measure    string  `json:"measure" v:"required" dc:"份量描述"`
	Grams      float64 `json:"grams" v:"required|min:0" dc:"克数"`
	Calories   float64 `json:"calories" v:"required|min:0" dc:"卡路里"`
	Protein    float64 `json:"protein" v:"required|min:0" dc:"蛋白质(g)"`
	Fat        float64 `json:"fat" v:"required|min:0" dc:"脂肪(g)"`
	SatFat     float64 `json:"satFat" v:"min:0" dc:"饱和脂肪(g)"`
	Carbs      float64 `json:"carbs" v:"required|min:0" dc:"碳水(g)"`
	CategoryId int     `json:"categoryId" v:"required" dc:"分类ID"`
	ImageUrl   string  `json:"imageUrl" dc:"图片URL"`
	Unit       string  `json:"unit" dc:"单位"`
	Desc       string  `json:"desc" dc:"描述信息"`
	Remark     string  `json:"remark" dc:"备注"`
}

type FoodBatchImportRes struct {
	SuccessCount int      `json:"successCount" dc:"成功导入数量"`
	FailCount    int      `json:"failCount" dc:"失败数量"`
	FailReasons  []string `json:"failReasons" dc:"失败原因列表"`
	TotalCount   int      `json:"totalCount" dc:"总数量"`
}

// 分类详情查询请求
type CategoryDetailReq struct {
	g.Meta `path:"/category/{id}" tags:"Food" method:"get" summary:"获取食物分类详情"`
	Id     int `json:"id" v:"required" dc:"分类ID"`
}

type CategoryDetailRes struct {
	Id          int    `json:"id" dc:"分类ID"`
	Name        string `json:"name" dc:"分类名称"`
	Description string `json:"description" dc:"分类描述"`
	Color       string `json:"color" dc:"分类颜色"`
	SortOrder   int    `json:"sortOrder" dc:"排序顺序"`
	FoodCount   int    `json:"foodCount" dc:"该分类下的食物数量"`
}

package adminuser

import (
	"context"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"

	"shikeyinxiang-goframe/api/adminuser/v1"
)

func (c *ControllerV1) ChangePassword(ctx context.Context, req *v1.ChangePasswordReq) (res *v1.ChangePasswordRes, err error) {
	// 1. 从上下文获取当前用户信息
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.UserChangePasswordInput{
		UserId:      user.UserId,
		OldPassword: req.OldPassword,
		NewPassword: req.NewPassword,
	}

	// 3. 调用业务逻辑
	output, err := service.User().ChangePassword(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.ChangePasswordRes{
		Success: output.Success,
		Message: output.Message,
	}

	return res, nil
}

package dashboard

import (
	"context"

	"shikeyinxiang-goframe/api/dashboard/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) NutritionTrend(ctx context.Context, req *v1.NutritionTrendReq) (res *v1.NutritionTrendRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.DashboardNutritionTrendInput{
		Period: req.Period,
	}

	// 2. 调用业务逻辑
	output, err := service.Dashboard().GetNutritionTrend(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.NutritionTrendRes{
		Period:      output.Trend.Period,
		DateList:    output.Trend.DateList,
		CalorieList: output.Trend.CalorieList,
		ProteinList: output.Trend.ProteinList,
		CarbsList:   output.Trend.CarbsList,
		FatList:     output.Trend.FatList,
		DataPoints:  output.Trend.DataPoints,
	}

	return res, nil
}

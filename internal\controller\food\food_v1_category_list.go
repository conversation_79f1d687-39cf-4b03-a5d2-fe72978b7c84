package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) CategoryList(ctx context.Context, req *v1.CategoryListReq) (res *v1.CategoryListRes, err error) {
	// 1. 判断是否分页查询
	if req.Page != nil && req.Size != nil {
		// 分页查询
		output, err := service.FoodCategory().GetCategoryList(ctx, *req.Page, *req.Size)
		if err != nil {
			return nil, err
		}

		// 转换分类列表格式
		var categoryList []v1.FoodCategory
		for _, category := range output.List {
			categoryList = append(categoryList, v1.FoodCategory{
				Id:          category.Id,
				Name:        category.Name,
				Description: category.Description,
				Color:       category.Color,
				SortOrder:   category.SortOrder,
				FoodCount:   category.FoodCount,
			})
		}

		res = &v1.CategoryListRes{
			List:  categoryList,
			Total: output.Total,
			Page:  req.Page,
			Size:  req.Size,
		}
	} else {
		// 全量查询
		categories, err := service.FoodCategory().GetAllCategories(ctx)
		if err != nil {
			return nil, err
		}

		// 转换分类列表格式
		var categoryList []v1.FoodCategory
		for _, category := range categories {
			categoryList = append(categoryList, v1.FoodCategory{
				Id:          category.Id,
				Name:        category.Name,
				Description: category.Description,
				Color:       category.Color,
				SortOrder:   category.SortOrder,
				FoodCount:   category.FoodCount,
			})
		}

		res = &v1.CategoryListRes{
			List:  categoryList,
			Total: int64(len(categoryList)),
			Page:  nil,
			Size:  nil,
		}
	}

	return res, nil
}

package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) FoodBatchImport(ctx context.Context, req *v1.FoodBatchImportReq) (res *v1.FoodBatchImportRes, err error) {
	// 1. 转换食物导入项为模型输入
	var foods []*model.FoodCreateInput
	for _, item := range req.Foods {
		foods = append(foods, &model.FoodCreateInput{
			Name:       item.Name,
			Measure:    item.Measure,
			Grams:      item.Grams,
			Calories:   item.Calories,
			Protein:    item.Protein,
			Fat:        item.Fat,
			SatFat:     item.SatFat,
			Carbs:      item.Carbs,
			CategoryId: item.CategoryId,
			ImageUrl:   item.ImageUrl,
		})
	}

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.FoodBatchImportInput{
		Foods: foods,
	}

	// 3. 调用业务逻辑
	output, err := service.Food().BatchImportFoods(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.FoodBatchImportRes{
		SuccessCount: output.SuccessCount,
		FailCount:    output.FailCount,
		FailReasons:  []string{output.Message}, // 简化处理，实际可能需要更详细的失败原因
		TotalCount:   len(req.Foods),
	}

	return res, nil
}

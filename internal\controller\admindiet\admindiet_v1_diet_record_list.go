package admindiet

import (
	"context"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"

	"shikeyinxiang-goframe/api/admindiet/v1"
)

func (c *ControllerV1) DietRecordList(ctx context.Context, req *v1.DietRecordListReq) (res *v1.DietRecordListRes, err error) {
	// 1. 判断是否为管理员查询
	var input interface{}
	if req.UserId != nil {
		// 管理员查询所有用户记录
		input = &model.DietRecordAdminQueryInput{
			UserId:    req.UserId,
			StartDate: req.StartDate,
			EndDate:   req.EndDate,
			MealType:  req.MealType,
			Page:      req.Page,
			Size:      req.Size,
		}
	} else {
		// 普通用户查询自己的记录
		user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)
		input = &model.DietRecordQueryInput{
			UserId:    user.UserId,
			StartDate: req.StartDate,
			EndDate:   req.EndDate,
			MealType:  req.MealType,
			Page:      req.Page,
			Size:      req.Size,
		}
	}

	// 2. 调用对应的业务逻辑
	var output interface{}
	if req.UserId != nil {
		// 管理员查询
		adminOutput, err := service.Diet().GetAllUsersDietRecords(ctx, input.(*model.DietRecordAdminQueryInput))
		if err != nil {
			return nil, err
		}
		output = adminOutput
	} else {
		// 普通用户查询
		userOutput, err := service.Diet().GetDietRecords(ctx, input.(*model.DietRecordQueryInput))
		if err != nil {
			return nil, err
		}
		output = userOutput
	}

	// 3. 转换记录列表格式
	var records []v1.DietRecordItem
	var total int64
	var page, size int

	if adminOutput, ok := output.(*model.DietRecordAdminQueryOutput); ok {
		// 管理员查询结果
		for _, record := range adminOutput.List {
			// 转换食物列表
			var foods []v1.DietRecordFoodItem
			for _, food := range record.Foods {
				foods = append(foods, v1.DietRecordFoodItem{
					FoodId:   food.FoodId,
					Name:     food.Name,
					Amount:   food.Amount,
					Unit:     food.Unit,
					Calories: food.Calories,
					Protein:  food.Protein,
					Fat:      food.Fat,
					Carbs:    food.Carbs,
					Grams:    food.Grams,
				})
			}

			records = append(records, v1.DietRecordItem{
				Id:           record.Id,
				UserId:       record.UserId,
				Username:     record.Username,
				Date:         record.Date,
				Time:         record.Time,
				MealType:     record.MealType,
				Remark:       record.Remark,
				TotalCalorie: record.TotalCalorie,
				Foods:        foods,
			})
		}
		total = int64(adminOutput.Total)
		page = adminOutput.Page
		size = adminOutput.Size
	} else if userOutput, ok := output.(*model.DietRecordQueryOutput); ok {
		// 普通用户查询结果
		for _, record := range userOutput.List {
			// 转换食物列表
			var foods []v1.DietRecordFoodItem
			for _, food := range record.Foods {
				foods = append(foods, v1.DietRecordFoodItem{
					FoodId:   food.FoodId,
					Name:     food.Name,
					Amount:   food.Amount,
					Unit:     food.Unit,
					Calories: food.Calories,
					Protein:  food.Protein,
					Fat:      food.Fat,
					Carbs:    food.Carbs,
					Grams:    food.Grams,
				})
			}

			records = append(records, v1.DietRecordItem{
				Id:           record.Id,
				UserId:       record.UserId,
				Username:     record.Username,
				Date:         record.Date,
				Time:         record.Time,
				MealType:     record.MealType,
				Remark:       record.Remark,
				TotalCalorie: record.TotalCalorie,
				Foods:        foods,
			})
		}
		total = int64(userOutput.Total)
		page = userOutput.Page
		size = userOutput.Size
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.DietRecordListRes{
		List:  records,
		Total: total,
		Page:  page,
		Size:  size,
	}

	return res, nil
}

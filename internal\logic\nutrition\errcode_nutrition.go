package nutrition

import "github.com/gogf/gf/v2/errors/gcode"

// 营养分析模块错误码定义
// 错误码格式：AABBBCCC (AA=60营养模块, BBB=001-999功能, CCC=001-999具体错误)

// ==================== 营养分析基础错误 (60001xxx) ====================

var (
	// CodeNutritionInvalidUser 用户ID无效
	CodeNutritionInvalidUser = gcode.New(60001001, "用户ID无效", nil)
	
	// CodeNutritionInvalidDate 日期无效
	CodeNutritionInvalidDate = gcode.New(60001002, "日期无效", nil)
	
	// CodeNutritionInvalidPeriod 时间周期无效
	CodeNutritionInvalidPeriod = gcode.New(60001003, "时间周期无效", nil)
	
	// CodeNutritionCalculationFailed 营养计算失败
	CodeNutritionCalculationFailed = gcode.New(60001004, "营养计算失败", nil)
	
	// CodeNutritionDataNotFound 营养数据不存在
	CodeNutritionDataNotFound = gcode.New(60001005, "营养数据不存在", nil)
)

// ==================== 营养目标相关错误 (60002xxx) ====================

var (
	// CodeNutritionGoalNotFound 营养目标不存在
	CodeNutritionGoalNotFound = gcode.New(60002001, "营养目标不存在", nil)
	
	// CodeNutritionGoalQueryFailed 查询营养目标失败
	CodeNutritionGoalQueryFailed = gcode.New(60002002, "查询营养目标失败", nil)
	
	// CodeNutritionGoalInvalid 营养目标无效
	CodeNutritionGoalInvalid = gcode.New(60002003, "营养目标无效", nil)
)

// ==================== 营养统计相关错误 (60003xxx) ====================

var (
	// CodeNutritionStatCalculationFailed 营养统计计算失败
	CodeNutritionStatCalculationFailed = gcode.New(60003001, "营养统计计算失败", nil)
	
	// CodeNutritionTrendCalculationFailed 营养趋势计算失败
	CodeNutritionTrendCalculationFailed = gcode.New(60003002, "营养趋势计算失败", nil)
	
	// CodeNutritionComplianceCalculationFailed 营养达标率计算失败
	CodeNutritionComplianceCalculationFailed = gcode.New(60003003, "营养达标率计算失败", nil)
)

// ==================== 营养建议相关错误 (60004xxx) ====================

var (
	// CodeNutritionAdviceGenerationFailed 营养建议生成失败
	CodeNutritionAdviceGenerationFailed = gcode.New(60004001, "营养建议生成失败", nil)

	// CodeNutritionAdviceNotFound 营养建议不存在
	CodeNutritionAdviceNotFound = gcode.New(60004002, "营养建议不存在", nil)

	// CodeNutritionAdviceQueryFailed 查询营养建议失败
	CodeNutritionAdviceQueryFailed = gcode.New(60004003, "查询营养建议失败", nil)

	// CodeNutritionInvalidAdviceId 营养建议ID无效
	CodeNutritionInvalidAdviceId = gcode.New(60004004, "营养建议ID无效", nil)

	// CodeNutritionInvalidAdviceType 营养建议类型无效
	CodeNutritionInvalidAdviceType = gcode.New(60004005, "营养建议类型无效", nil)

	// CodeNutritionInvalidAdviceTitle 营养建议标题无效
	CodeNutritionInvalidAdviceTitle = gcode.New(60004006, "营养建议标题无效", nil)

	// CodeNutritionInvalidConditionType 条件类型无效
	CodeNutritionInvalidConditionType = gcode.New(60004007, "条件类型无效", nil)
)

// ==================== 数据依赖相关错误 (60005xxx) ====================

var (
	// CodeNutritionDietRecordQueryFailed 查询饮食记录失败
	CodeNutritionDietRecordQueryFailed = gcode.New(60005001, "查询饮食记录失败", nil)
	
	// CodeNutritionUserDataQueryFailed 查询用户数据失败
	CodeNutritionUserDataQueryFailed = gcode.New(60005002, "查询用户数据失败", nil)
	
	// CodeNutritionDataIntegrityError 营养数据完整性错误
	CodeNutritionDataIntegrityError = gcode.New(60005003, "营养数据完整性错误", nil)
)

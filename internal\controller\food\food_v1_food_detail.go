package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) FoodDetail(ctx context.Context, req *v1.FoodDetailReq) (res *v1.FoodDetailRes, err error) {
	// 1. 调用业务逻辑 - 直接通过ID获取食物信息
	foodInfo, err := service.Food().GetFoodByID(ctx, req.Id)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 2. 参数转换：业务层Output → API响应
	res = &v1.FoodDetailRes{
		Id:         foodInfo.Id,
		Name:       foodInfo.Name,
		Measure:    foodInfo.Measure,
		Grams:      foodInfo.Grams,
		Calories:   foodInfo.Calories,
		Protein:    foodInfo.Protein,
		Fat:        foodInfo.Fat,
		SatFat:     foodInfo.SatFat,
		Carbs:      foodInfo.Carbs,
		Category:   foodInfo.Category,
		CategoryId: foodInfo.CategoryId,
		ImageUrl:   foodInfo.ImageUrl,
		Unit:       "", // 根据需要设置
		Desc:       "", // 根据需要设置
		Remark:     "", // 根据需要设置
	}

	return res, nil
}

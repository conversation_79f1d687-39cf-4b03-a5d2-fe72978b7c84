package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) DailyNutrition(ctx context.Context, req *v1.DailyNutritionReq) (res *v1.DailyNutritionRes, err error) {
	// 1. 从上下文获取当前用户信息
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.NutritionDailyStatInput{
		UserId: user.UserId,
		Date:   req.Date,
	}

	// 3. 调用业务逻辑
	output, err := service.Nutrition().GetDailyNutritionStat(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.DailyNutritionRes{
		Date:              output.Stat.Date,
		Calorie:           output.Stat.Calorie,
		Protein:           output.Stat.Protein,
		Carbs:             output.Stat.Carbs,
		Fat:               output.Stat.Fat,
		CaloriePercentage: output.Stat.CaloriePercentage,
		ProteinPercentage: output.Stat.ProteinPercentage,
		CarbsPercentage:   output.Stat.CarbsPercentage,
		FatPercentage:     output.Stat.FatPercentage,
	}

	return res, nil
}

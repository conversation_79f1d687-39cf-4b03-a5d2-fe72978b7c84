package nutrition

import (
	"context"
	"time"

	"shikeyinxiang-goframe/api/nutrition/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) ComplianceRate(ctx context.Context, req *v1.ComplianceRateReq) (res *v1.ComplianceRateRes, err error) {
	// 1. 处理日期参数，如果为空则使用当天
	date := req.Date
	if date == "" {
		date = time.Now().Format("2006-01-02")
	}

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.NutritionComplianceRateInput{
		Date: date,
	}

	// 3. 调用业务逻辑
	output, err := service.Nutrition().CalculateNutritionComplianceRate(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 获取活跃用户数据用于响应
	activeUsers, err := service.Diet().GetActiveUserIds(ctx, &model.DietRecordActiveUsersInput{
		Date: &date,
	})
	if err != nil {
		// 如果获取活跃用户失败，设置默认值
		activeUsers = &model.DietRecordActiveUsersOutput{UserIds: []int64{}}
	}

	totalUsers := int64(len(activeUsers.UserIds))
	compliantUsers := int64(float64(totalUsers) * output.ComplianceRate / 100)

	// 5. 参数转换：业务层Output → API响应
	res = &v1.ComplianceRateRes{
		Date:           date,
		ComplianceRate: output.ComplianceRate,
		TotalUsers:     totalUsers,
		CompliantUsers: compliantUsers,
	}

	return res, nil
}

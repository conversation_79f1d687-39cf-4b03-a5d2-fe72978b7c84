package user

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"
	"golang.org/x/crypto/bcrypt"

	"shikeyinxiang-goframe/internal/dao"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/model/do"
	"shikeyinxiang-goframe/internal/model/entity"
	"shikeyinxiang-goframe/internal/service"
)

type sUser struct{}

func init() {
	service.RegisterUser(New())
}

func New() service.IUser {
	return &sUser{}
}

// GetUserByEmail 根据邮箱获取用户
func (s *sUser) GetUserByEmail(ctx context.Context, email string) (*model.UserInfo, error) {
	var user *entity.User
	err := dao.User.Ctx(ctx).Where(dao.User.Columns().Email, email).Scan(&user)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, nil
	}
	return s.convertToUserInfo(user), nil
}

// GetUserByUsername 根据用户名获取用户
func (s *sUser) GetUserByUsername(ctx context.Context, username string) (*model.UserInfo, error) {
	var user *entity.User
	err := dao.User.Ctx(ctx).Where(dao.User.Columns().Username, username).Scan(&user)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, nil
	}
	return s.convertToUserInfo(user), nil
}

// GetUserByOpenid 根据微信openid获取用户
func (s *sUser) GetUserByOpenid(ctx context.Context, openid string) (*model.UserInfo, error) {
	var user *entity.User
	err := dao.User.Ctx(ctx).Where(dao.User.Columns().Openid, openid).Scan(&user)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, nil
	}
	return s.convertToUserInfo(user), nil
}

// GetUserByID 根据用户ID获取用户
func (s *sUser) GetUserByID(ctx context.Context, userId int64) (*model.UserInfo, error) {
	var user *entity.User
	err := dao.User.Ctx(ctx).Where(dao.User.Columns().Id, userId).Scan(&user)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, nil
	}
	return s.convertToUserInfo(user), nil
}

// CreateUser 创建用户
func (s *sUser) CreateUser(ctx context.Context, in *model.UserCreateInput) (*model.UserInfo, error) {
	// 1. 数据验证
	if err := s.validateUserCreateInput(ctx, in); err != nil {
		return nil, err
	}

	// 2. 检查用户名和邮箱是否已存在
	if err := s.checkUserExists(ctx, in.Username, in.Email, in.Openid); err != nil {
		return nil, err
	}

	// 3. 处理密码
	password := in.Password
	if password == "" {
		password = s.generateRandomPassword()
	}

	// 验证密码强度
	if err := s.validatePassword(password); err != nil {
		return nil, err
	}

	// 加密密码
	hashedPassword, err := s.hashPassword(password)
	if err != nil {
		return nil, gerror.New("密码加密失败")
	}

	// 4. 设置默认值
	if in.Role == "" {
		in.Role = "USER"
	}
	if in.Status == 0 {
		in.Status = 1
	}

	// 5. 创建用户记录
	userDO := &do.User{
		Username:   in.Username,
		Email:      in.Email,
		Password:   hashedPassword,
		Role:       in.Role,
		Status:     in.Status,
		AvatarUrl:  in.AvatarUrl,
		Openid:     in.Openid,
		CreateTime: gtime.Now(),
	}

	result, err := dao.User.Ctx(ctx).Data(userDO).Insert()
	if err != nil {
		// 处理数据库约束错误
		if strings.Contains(err.Error(), "username") {
			return nil, gerror.NewCode(CodeUserUsernameExists, "用户名已存在")
		}
		if strings.Contains(err.Error(), "email") {
			return nil, gerror.NewCode(CodeUserEmailExists, "邮箱已被使用")
		}
		return nil, err
	}

	// 6. 获取创建的用户ID
	userId, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	// 7. 返回创建的用户信息
	return s.GetUserByID(ctx, userId)
}

// UpdateUser 更新用户信息
func (s *sUser) UpdateUser(ctx context.Context, in *model.UserUpdateInput) (*model.UserUpdateOutput, error) {
	// 1. 验证用户是否存在
	existUser, err := s.GetUserByID(ctx, in.Id)
	if err != nil {
		return nil, err
	}
	if existUser == nil {
		return nil, gerror.NewCode(CodeUserNotFound, "用户不存在")
	}

	// 2. 构建更新数据
	updateData := &do.User{}

	if in.Username != "" {
		// 检查用户名是否已被其他用户使用
		if err := s.checkUsernameExists(ctx, in.Username, in.Id); err != nil {
			return nil, err
		}
		updateData.Username = in.Username
	}

	if in.Email != "" {
		// 验证邮箱格式
		if !s.isValidEmail(in.Email) {
			return nil, gerror.NewCode(CodeUserInvalidEmail, "邮箱格式无效")
		}
		// 检查邮箱是否已被其他用户使用
		if err := s.checkEmailExists(ctx, in.Email, in.Id); err != nil {
			return nil, err
		}
		updateData.Email = in.Email
	}

	if in.Role != "" {
		updateData.Role = in.Role
	}

	if in.Status != 0 {
		updateData.Status = in.Status
	}

	if in.AvatarUrl != "" {
		updateData.AvatarUrl = in.AvatarUrl
	}

	// 处理密码更新
	if in.Password != "" {
		if err := s.validatePassword(in.Password); err != nil {
			return nil, err
		}
		hashedPassword, err := s.hashPassword(in.Password)
		if err != nil {
			return nil, gerror.New("密码加密失败")
		}
		updateData.Password = hashedPassword
	}

	// 3. 执行更新
	_, err = dao.User.Ctx(ctx).Where(dao.User.Columns().Id, in.Id).Data(updateData).Update()
	if err != nil {
		return &model.UserUpdateOutput{
			Success: false,
			Message: "更新失败: " + err.Error(),
		}, nil
	}

	return &model.UserUpdateOutput{
		Success: true,
		Message: "更新成功",
	}, nil
}

// UpdateUserStatus 更新用户状态
func (s *sUser) UpdateUserStatus(ctx context.Context, in *model.UserStatusUpdateInput) (*model.UserStatusUpdateOutput, error) {
	// 验证用户是否存在
	existUser, err := s.GetUserByID(ctx, in.UserId)
	if err != nil {
		return nil, err
	}
	if existUser == nil {
		return nil, gerror.NewCode(CodeUserNotFound, "用户不存在")
	}

	// 更新状态
	_, err = dao.User.Ctx(ctx).Where(dao.User.Columns().Id, in.UserId).Data(do.User{
		Status: in.Status,
	}).Update()

	if err != nil {
		return &model.UserStatusUpdateOutput{
			Success: false,
			Message: "状态更新失败: " + err.Error(),
		}, nil
	}

	return &model.UserStatusUpdateOutput{
		Success: true,
		Message: "状态更新成功",
	}, nil
}

// GetUserList 分页查询用户列表
func (s *sUser) GetUserList(ctx context.Context, in *model.UserQueryInput) (*model.UserListOutput, error) {
	// 构建查询条件
	query := dao.User.Ctx(ctx)

	// 状态筛选
	if in.Status != nil {
		query = query.Where(dao.User.Columns().Status, *in.Status)
	}

	// 角色筛选
	if in.Role != "" {
		query = query.Where(dao.User.Columns().Role, in.Role)
	}

	// 关键词搜索
	if in.Keyword != "" {
		keyword := "%" + in.Keyword + "%"
		query = query.Where(fmt.Sprintf("(%s LIKE ? OR %s LIKE ?)",
			dao.User.Columns().Username, dao.User.Columns().Email), keyword, keyword)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, err
	}

	// 分页查询
	offset := (in.Page - 1) * in.Size
	var users []*entity.User
	err = query.Limit(in.Size).Offset(offset).OrderDesc(dao.User.Columns().CreateTime).Scan(&users)
	if err != nil {
		return nil, err
	}

	// 转换为UserInfo
	userInfos := make([]*model.UserInfo, 0, len(users))
	for _, user := range users {
		userInfos = append(userInfos, s.convertToUserInfo(user))
	}

	return &model.UserListOutput{
		List:  userInfos,
		Total: int64(total),
		Page:  in.Page,
		Size:  in.Size,
	}, nil
}

// VerifyPassword 验证密码
func (s *sUser) VerifyPassword(ctx context.Context, in *model.PasswordVerifyInput) (*model.PasswordVerifyOutput, error) {
	// 查找用户
	var user *entity.User
	err := dao.User.Ctx(ctx).Where(fmt.Sprintf("(%s = ? OR %s = ?)",
		dao.User.Columns().Username, dao.User.Columns().Email),
		in.UsernameOrEmail, in.UsernameOrEmail).Scan(&user)

	if err != nil {
		return &model.PasswordVerifyOutput{
			Valid:   false,
			Message: "查询用户失败",
		}, nil
	}

	if user == nil {
		return &model.PasswordVerifyOutput{
			Valid:   false,
			Message: "用户不存在",
		}, nil
	}

	// 验证密码
	valid := s.verifyPassword(in.Password, user.Password)
	message := "密码验证成功"
	if !valid {
		message = "密码错误"
	}

	return &model.PasswordVerifyOutput{
		Valid:   valid,
		Message: message,
	}, nil
}

// ChangePassword 修改密码
func (s *sUser) ChangePassword(ctx context.Context, in *model.UserChangePasswordInput) (*model.UserChangePasswordOutput, error) {
	// 验证用户是否存在
	existUser, err := s.GetUserByID(ctx, in.UserId)
	if err != nil {
		return nil, err
	}
	if existUser == nil {
		return nil, gerror.NewCode(CodeUserNotFound, "用户不存在")
	}

	// 验证原密码是否正确
	if !s.verifyPassword(in.OldPassword, existUser.Password) {
		return &model.UserChangePasswordOutput{
			Success: false,
			Message: "原密码不正确",
		}, nil
	}

	// 验证新密码强度
	if err := s.validatePassword(in.NewPassword); err != nil {
		return &model.UserChangePasswordOutput{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	// 验证新密码与原密码不能相同
	if in.OldPassword == in.NewPassword {
		return &model.UserChangePasswordOutput{
			Success: false,
			Message: "新密码不能与原密码相同",
		}, nil
	}

	// 加密新密码
	hashedPassword, err := s.hashPassword(in.NewPassword)
	if err != nil {
		return nil, gerror.New("密码加密失败")
	}

	// 更新密码
	_, err = dao.User.Ctx(ctx).Where(dao.User.Columns().Id, in.UserId).Data(do.User{
		Password: hashedPassword,
	}).Update()
	if err != nil {
		return nil, gerror.Wrap(err, "密码更新失败")
	}

	return &model.UserChangePasswordOutput{
		Success: true,
		Message: "密码修改成功",
	}, nil
}

// UpdateUserAvatar 更新用户头像
func (s *sUser) UpdateUserAvatar(ctx context.Context, in *model.UserAvatarUpdateInput) (*model.UserAvatarUpdateOutput, error) {
	// 验证用户是否存在
	existUser, err := s.GetUserByID(ctx, in.UserId)
	if err != nil {
		return nil, err
	}
	if existUser == nil {
		return nil, gerror.NewCode(CodeUserNotFound, "用户不存在")
	}

	// 更新头像URL
	_, err = dao.User.Ctx(ctx).Where(dao.User.Columns().Id, in.UserId).Data(do.User{
		AvatarUrl: in.AvatarUrl,
	}).Update()

	if err != nil {
		return &model.UserAvatarUpdateOutput{
			Success: false,
			Message: "头像更新失败: " + err.Error(),
		}, nil
	}

	return &model.UserAvatarUpdateOutput{
		Success: true,
		Message: "头像更新成功",
	}, nil
}

// GenerateAvatarUploadUrl 生成头像上传URL
func (s *sUser) GenerateAvatarUploadUrl(ctx context.Context, in *model.AvatarUploadUrlInput) (*model.AvatarUploadUrlOutput, error) {
	// 验证用户是否存在
	existUser, err := s.GetUserByID(ctx, in.UserId)
	if err != nil {
		return nil, err
	}
	if existUser == nil {
		return nil, gerror.NewCode(CodeUserNotFound, "用户不存在")
	}

	// 验证文件类型
	if !s.isValidImageType(in.ContentType) {
		return nil, gerror.New("不支持的文件类型")
	}

	// 生成文件名
	fileName := fmt.Sprintf("avatar_%d_%d.%s", in.UserId, time.Now().Unix(), s.getFileExtension(in.ContentType))

	// 调用文件服务生成上传URL
	// 这里需要集成文件服务，暂时返回模拟数据
	uploadUrl := fmt.Sprintf("https://example.com/upload/%s", fileName)
	avatarUrl := fmt.Sprintf("https://example.com/avatar/%s", fileName)

	return &model.AvatarUploadUrlOutput{
		UploadUrl: uploadUrl,
		FileName:  fileName,
		AvatarUrl: avatarUrl,
	}, nil
}

// GetUsersByIds 批量查询用户
func (s *sUser) GetUsersByIds(ctx context.Context, in *model.UserBatchQueryInput) (*model.UserBatchQueryOutput, error) {
	if len(in.UserIds) == 0 {
		return &model.UserBatchQueryOutput{
			Users: []model.UserItem{},
		}, nil
	}

	var users []entity.User
	err := dao.User.Ctx(ctx).WhereIn(dao.User.Columns().Id, in.UserIds).Scan(&users)
	if err != nil {
		return nil, gerror.Wrap(err, "批量查询失败")
	}

	var userItems []model.UserItem
	for _, user := range users {
		userItems = append(userItems, model.UserItem{
			Id:       user.Id,
			Username: user.Username,
			Email:    user.Email,
			Role:     user.Role,
			Status:   user.Status,
		})
	}

	return &model.UserBatchQueryOutput{
		Users: userItems,
	}, nil
}

// GetUserNutritionGoal 获取用户营养目标
func (s *sUser) GetUserNutritionGoal(ctx context.Context, in *model.UserNutritionGoalQueryInput) (*model.UserNutritionGoalQueryOutput, error) {
	if in.UserId <= 0 {
		return nil, gerror.NewCode(CodeUserNotFound, "用户ID无效")
	}

	// 查询用户营养目标
	var goal entity.UserNutritionGoals
	err := dao.UserNutritionGoals.Ctx(ctx).Where(dao.UserNutritionGoals.Columns().UserId, in.UserId).Scan(&goal)
	if err != nil {
		return nil, gerror.Wrap(err, "查询营养目标失败")
	}

	// 如果没有设置营养目标，返回默认值
	if goal.UserId == 0 {
		goal = entity.UserNutritionGoals{
			UserId:        in.UserId,
			CalorieTarget: 2000, // 默认热量目标
			ProteinTarget: 150,  // 默认蛋白质目标
			CarbsTarget:   250,  // 默认碳水目标
			FatTarget:     67,   // 默认脂肪目标
		}
	}

	return &model.UserNutritionGoalQueryOutput{
		Goal: model.UserNutritionGoal{
			UserId:      goal.UserId,
			CalorieGoal: int(goal.CalorieTarget),
			ProteinGoal: float64(goal.ProteinTarget),
			CarbsGoal:   float64(goal.CarbsTarget),
			FatGoal:     float64(goal.FatTarget),
		},
	}, nil
}

// UpdateUserNutritionGoal 更新用户营养目标
func (s *sUser) UpdateUserNutritionGoal(ctx context.Context, in *model.UserNutritionGoalUpdateInput) (*model.UserNutritionGoalUpdateOutput, error) {
	if in.UserId <= 0 {
		return nil, gerror.NewCode(CodeUserNotFound, "用户ID无效")
	}

	// 验证营养目标值的合理性
	if in.DailyCalorie < 800 || in.DailyCalorie > 5000 {
		return nil, gerror.New("热量目标应在800-5000千卡之间")
	}
	if in.DailyProtein < 0 || in.DailyProtein > 500 {
		return nil, gerror.New("蛋白质目标应在0-500克之间")
	}
	if in.DailyCarbs < 0 || in.DailyCarbs > 1000 {
		return nil, gerror.New("碳水化合物目标应在0-1000克之间")
	}
	if in.DailyFat < 0 || in.DailyFat > 300 {
		return nil, gerror.New("脂肪目标应在0-300克之间")
	}

	// 检查用户是否存在
	var user entity.User
	err := dao.User.Ctx(ctx).Where(dao.User.Columns().Id, in.UserId).Scan(&user)
	if err != nil {
		return nil, gerror.Wrap(err, "查询用户失败")
	}
	if user.Id == 0 {
		return nil, gerror.NewCode(CodeUserNotFound, "用户不存在")
	}

	// 更新或插入营养目标
	goalData := do.UserNutritionGoals{
		UserId:        in.UserId,
		CalorieTarget: int(in.DailyCalorie),
		ProteinTarget: int(in.DailyProtein),
		CarbsTarget:   int(in.DailyCarbs),
		FatTarget:     int(in.DailyFat),
		UpdatedAt:     gtime.Now(),
	}

	// 先尝试更新
	result, err := dao.UserNutritionGoals.Ctx(ctx).
		Where(dao.UserNutritionGoals.Columns().UserId, in.UserId).
		Update(goalData)
	if err != nil {
		return nil, gerror.Wrap(err, "更新营养目标失败")
	}

	// 如果没有更新任何记录，说明记录不存在，需要插入
	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		goalData.CreatedAt = gtime.Now()
		_, err = dao.UserNutritionGoals.Ctx(ctx).Insert(goalData)
		if err != nil {
			return nil, gerror.Wrap(err, "创建营养目标失败")
		}
	}

	return &model.UserNutritionGoalUpdateOutput{
		Success: true,
		Message: "营养目标更新成功",
	}, nil
}

// GetTotalUserCount 获取用户总数
func (s *sUser) GetTotalUserCount(ctx context.Context) (int64, error) {
	count, err := dao.User.Ctx(ctx).Count()
	if err != nil {
		return 0, gerror.Wrap(err, "查询用户总数失败")
	}

	return int64(count), nil
}

// convertToUserInfo 将entity.User转换为model.UserInfo
func (s *sUser) convertToUserInfo(user *entity.User) *model.UserInfo {
	if user == nil {
		return nil
	}

	return &model.UserInfo{
		Id:         user.Id,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		CreateTime: user.CreateTime,
		AvatarUrl:  user.AvatarUrl,
		Openid:     user.Openid,
		Password:   user.Password, // 内部使用时包含密码，对外API时需要过滤
	}
}

// validateUserCreateInput 验证用户创建输入参数
func (s *sUser) validateUserCreateInput(ctx context.Context, in *model.UserCreateInput) error {
	if in.Username == "" {
		return gerror.New("用户名不能为空")
	}

	if len(in.Username) < 3 || len(in.Username) > 20 {
		return gerror.New("用户名长度必须在3-20个字符之间")
	}

	if in.Email == "" {
		return gerror.New("邮箱不能为空")
	}

	if !s.isValidEmail(in.Email) {
		return gerror.NewCode(CodeUserInvalidEmail, "邮箱格式无效")
	}

	return nil
}

// checkUserExists 检查用户是否已存在
func (s *sUser) checkUserExists(ctx context.Context, username, email, openid string) error {
	// 检查用户名
	if username != "" {
		count, err := dao.User.Ctx(ctx).Where(dao.User.Columns().Username, username).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return gerror.NewCode(CodeUserUsernameExists, "用户名已存在")
		}
	}

	// 检查邮箱
	if email != "" {
		count, err := dao.User.Ctx(ctx).Where(dao.User.Columns().Email, email).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return gerror.NewCode(CodeUserEmailExists, "邮箱已被使用")
		}
	}

	// 检查openid
	if openid != "" {
		count, err := dao.User.Ctx(ctx).Where(dao.User.Columns().Openid, openid).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return gerror.New("该微信账号已绑定其他用户")
		}
	}

	return nil
}

// checkUsernameExists 检查用户名是否已被其他用户使用
func (s *sUser) checkUsernameExists(ctx context.Context, username string, excludeUserId int64) error {
	count, err := dao.User.Ctx(ctx).Where(dao.User.Columns().Username, username).
		WhereNot(dao.User.Columns().Id, excludeUserId).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return gerror.NewCode(CodeUserUsernameExists, "用户名已存在")
	}
	return nil
}

// checkEmailExists 检查邮箱是否已被其他用户使用
func (s *sUser) checkEmailExists(ctx context.Context, email string, excludeUserId int64) error {
	count, err := dao.User.Ctx(ctx).Where(dao.User.Columns().Email, email).
		WhereNot(dao.User.Columns().Id, excludeUserId).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return gerror.NewCode(CodeUserEmailExists, "邮箱已被使用")
	}
	return nil
}

// validatePassword 验证密码强度
func (s *sUser) validatePassword(password string) error {
	if len(password) < 6 {
		return gerror.NewCode(CodeUserPasswordTooWeak, "密码长度至少6位")
	}
	return nil
}

// hashPassword 加密密码
func (s *sUser) hashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// verifyPassword 验证密码
func (s *sUser) verifyPassword(plainPassword, hashedPassword string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(plainPassword))
	return err == nil
}

// generateRandomPassword 生成随机密码
func (s *sUser) generateRandomPassword() string {
	return fmt.Sprintf("temp_%d", time.Now().Unix())
}

// isValidEmail 验证邮箱格式
func (s *sUser) isValidEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// isValidImageType 验证图片类型
func (s *sUser) isValidImageType(contentType string) bool {
	validTypes := []string{"image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"}
	for _, validType := range validTypes {
		if contentType == validType {
			return true
		}
	}
	return false
}

// getFileExtension 根据content type获取文件扩展名
func (s *sUser) getFileExtension(contentType string) string {
	switch contentType {
	case "image/jpeg", "image/jpg":
		return "jpg"
	case "image/png":
		return "png"
	case "image/gif":
		return "gif"
	case "image/webp":
		return "webp"
	default:
		return "jpg"
	}
}

// GenerateAvatarDownloadUrl 生成头像下载URL
func (s *sUser) GenerateAvatarDownloadUrl(ctx context.Context, in *model.AvatarDownloadUrlInput) (*model.AvatarDownloadUrlOutput, error) {
	panic("implement me")
}

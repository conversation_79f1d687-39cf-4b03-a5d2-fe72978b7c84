package service

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
)

type IUser interface {
	// GetUserByEmail 根据邮箱获取用户
	GetUserByEmail(ctx context.Context, email string) (*model.UserInfo, error)

	// GetUserByUsername 根据用户名获取用户
	GetUserByUsername(ctx context.Context, username string) (*model.UserInfo, error)

	// GetUserByOpenid 根据微信openid获取用户
	GetUserByOpenid(ctx context.Context, openid string) (*model.UserInfo, error)

	// GetUserByID 根据用户ID获取用户
	GetUserByID(ctx context.Context, userId int64) (*model.UserInfo, error)

	// CreateUser 创建用户
	CreateUser(ctx context.Context, in *model.UserCreateInput) (*model.UserInfo, error)

	// UpdateUser 更新用户信息
	UpdateUser(ctx context.Context, in *model.UserUpdateInput) (*model.UserUpdateOutput, error)

	// UpdateUserStatus 更新用户状态
	UpdateUserStatus(ctx context.Context, in *model.UserStatusUpdateInput) (*model.UserStatusUpdateOutput, error)

	// GetUserList 分页查询用户列表
	GetUserList(ctx context.Context, in *model.UserQueryInput) (*model.UserListOutput, error)

	// GetTotalUserCount 获取用户总数
	GetTotalUserCount(ctx context.Context) (int64, error)

	// VerifyPassword 验证密码
	VerifyPassword(ctx context.Context, in *model.PasswordVerifyInput) (*model.PasswordVerifyOutput, error)

	// ChangePassword 修改密码
	ChangePassword(ctx context.Context, in *model.UserChangePasswordInput) (*model.UserChangePasswordOutput, error)

	// UpdateUserAvatar 更新用户头像
	UpdateUserAvatar(ctx context.Context, in *model.UserAvatarUpdateInput) (*model.UserAvatarUpdateOutput, error)

	// GenerateAvatarUploadUrl 生成头像上传URL
	GenerateAvatarUploadUrl(ctx context.Context, in *model.AvatarUploadUrlInput) (*model.AvatarUploadUrlOutput, error)

	// GenerateAvatarDownloadUrl 生成头像下载URL
	GenerateAvatarDownloadUrl(ctx context.Context, in *model.AvatarDownloadUrlInput) (*model.AvatarDownloadUrlOutput, error)

	// GetUsersByIds 批量查询用户
	GetUsersByIds(ctx context.Context, in *model.UserBatchQueryInput) (*model.UserBatchQueryOutput, error)

	// GetUserNutritionGoal 获取用户营养目标
	GetUserNutritionGoal(ctx context.Context, in *model.UserNutritionGoalQueryInput) (*model.UserNutritionGoalQueryOutput, error)

	// UpdateUserNutritionGoal 更新用户营养目标
	UpdateUserNutritionGoal(ctx context.Context, in *model.UserNutritionGoalUpdateInput) (*model.UserNutritionGoalUpdateOutput, error)
}

var localUser IUser

func User() IUser {
	if localUser == nil {
		panic("implement not found for interface IUser, forgot register?")
	}
	return localUser
}

func RegisterUser(i IUser) {
	localUser = i
}

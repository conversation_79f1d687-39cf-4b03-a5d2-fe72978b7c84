package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 用户登录请求
type UserLoginReq struct {
	g.Meta   `path:"/user/login" tags:"Auth" method:"post" summary:"用户登录"`
	Email    string `json:"email" v:"required|email" dc:"邮箱"`
	Password string `json:"password" v:"required" dc:"密码"`
}

type UserLoginRes struct {
	Token    string      `json:"token" dc:"JWT令牌"`
	UserInfo interface{} `json:"userInfo" dc:"用户信息"`
}

// 管理员登录请求
type AdminLoginReq struct {
	g.Meta   `path:"/admin/login" tags:"Auth" method:"post" summary:"管理员登录"`
	Username string `json:"username" v:"required" dc:"用户名"`
	Password string `json:"password" v:"required" dc:"密码"`
}

type AdminLoginRes struct {
	Token    string      `json:"token" dc:"JWT令牌"`
	UserInfo interface{} `json:"userInfo" dc:"用户信息"`
}

// 微信登录请求
type WechatLoginReq struct {
	g.<PERSON>a        `path:"/wechat-login" tags:"Auth" method:"post" summary:"微信登录"`
	Code          string `json:"code" v:"required" dc:"微信登录code"`
	EncryptedData string `json:"encryptedData" dc:"用户敏感数据"`
	Iv            string `json:"iv" dc:"偏移向量"`
}

type WechatLoginRes struct {
	Token    string      `json:"token" dc:"JWT令牌"`
	UserInfo interface{} `json:"userInfo" dc:"用户信息"`
}

// 用户注册请求
type RegisterReq struct {
	g.Meta    `path:"/register" tags:"Auth" method:"post" summary:"用户注册"`
	Username  string `json:"username" v:"required|length:3,20" dc:"用户名"`
	Email     string `json:"email" v:"required|email" dc:"邮箱"`
	Password  string `json:"password" v:"required|min:6" dc:"密码"`
	Phone     string `json:"phone" dc:"手机号"`
	Role      string `json:"role" dc:"角色"`
	Status    int    `json:"status" dc:"状态"`
	AvatarUrl string `json:"avatarUrl" dc:"头像URL"`
	Openid    string `json:"openid" dc:"微信openid"`
}

type RegisterRes struct {
	Id         int64  `json:"id" dc:"用户ID"`
	Username   string `json:"username" dc:"用户名"`
	Email      string `json:"email" dc:"邮箱"`
	Role       string `json:"role" dc:"角色"`
	Status     int    `json:"status" dc:"状态"`
	CreateTime string `json:"createTime" dc:"创建时间"`
	AvatarUrl  string `json:"avatarUrl" dc:"头像URL"`
}

// 登出请求
type LogoutReq struct {
	g.Meta `path:"/logout" tags:"Auth" method:"post" summary:"用户登出"`
}

type LogoutRes struct {
	Message string `json:"message" dc:"登出结果"`
}

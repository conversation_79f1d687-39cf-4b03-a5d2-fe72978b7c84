package diet

import (
	"context"

	"shikeyinxiang-goframe/api/diet/v1"
	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/middleware"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) DietRecordAdd(ctx context.Context, req *v1.DietRecordAddReq) (res *v1.DietRecordAddRes, err error) {
	// 1. 从上下文获取当前用户信息
	user := ctx.Value(consts.CtxUserInfo).(*middleware.UserInfo)

	// 2. 转换食物列表格式
	var foods []model.DietRecordFood
	for _, food := range req.Foods {
		foods = append(foods, model.DietRecordFood{
			FoodId:   food.FoodId,
			Name:     food.Name,
			Amount:   food.Amount,
			Unit:     food.Unit,
			Calories: food.Calories,
			Protein:  food.Protein,
			Fat:      food.Fat,
			Carbs:    food.Carbs,
			Grams:    food.Grams,
		})
	}

	// 3. 参数转换：API请求参数 → 业务层Input
	input := &model.DietRecordCreateInput{
		UserId:       user.UserId,
		Date:         req.Date,
		Time:         req.Time,
		MealType:     req.MealType,
		Remark:       req.Remark,
		TotalCalorie: req.TotalCalorie,
		Foods:        foods,
	}

	// 4. 调用业务逻辑
	output, err := service.Diet().CreateDietRecord(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 5. 参数转换：业务层Output → API响应
	res = &v1.DietRecordAddRes{
		RecordId: output.RecordId,
	}

	return res, nil
}

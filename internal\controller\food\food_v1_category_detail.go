package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) CategoryDetail(ctx context.Context, req *v1.CategoryDetailReq) (res *v1.CategoryDetailRes, err error) {
	// 1. 调用业务逻辑 - 直接通过ID获取分类信息
	categoryInfo, err := service.FoodCategory().GetCategoryByID(ctx, req.Id)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 2. 参数转换：业务层Output → API响应
	res = &v1.CategoryDetailRes{
		Id:          categoryInfo.Id,
		Name:        categoryInfo.Name,
		Description: categoryInfo.Description,
		Color:       categoryInfo.Color,
		SortOrder:   categoryInfo.SortOrder,
		FoodCount:   categoryInfo.FoodCount,
	}

	return res, nil
}

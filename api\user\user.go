// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
)

type IUserV1 interface {
	UserCreate(ctx context.Context, req *v1.UserCreateReq) (res *v1.UserCreateRes, err error)
	AvatarUploadUrl(ctx context.Context, req *v1.AvatarUploadUrlReq) (res *v1.AvatarUploadUrlRes, err error)
	AvatarDownloadUrl(ctx context.Context, req *v1.AvatarDownloadUrlReq) (res *v1.AvatarDownloadUrlRes, err error)
	CurrentUserInfo(ctx context.Context, req *v1.CurrentUserInfoReq) (res *v1.CurrentUserInfoRes, err error)
	CurrentUserUpdate(ctx context.Context, req *v1.CurrentUserUpdateReq) (res *v1.CurrentUserUpdateRes, err error)
	UserNutritionGoal(ctx context.Context, req *v1.UserNutritionGoalReq) (res *v1.UserNutritionGoalRes, err error)
	UserNutritionGoalUpdate(ctx context.Context, req *v1.UserNutritionGoalUpdateReq) (res *v1.UserNutritionGoalUpdateRes, err error)
	ChangePassword(ctx context.Context, req *v1.ChangePasswordReq) (res *v1.ChangePasswordRes, err error)
}

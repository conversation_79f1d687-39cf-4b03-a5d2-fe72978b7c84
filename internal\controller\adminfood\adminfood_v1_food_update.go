package adminfood

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"

	"shikeyinxiang-goframe/api/adminfood/v1"
)

func (c *ControllerV1) FoodUpdate(ctx context.Context, req *v1.FoodUpdateReq) (res *v1.FoodUpdateRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.FoodUpdateInput{
		Id:         req.Id,
		Name:       req.Name,
		Measure:    req.Measure,
		Grams:      req.Grams,
		Calories:   req.Calories,
		Protein:    req.Protein,
		Fat:        req.Fat,
		SatFat:     req.SatFat,
		Carbs:      req.Carbs,
		CategoryId: req.CategoryId,
		ImageUrl:   req.ImageUrl,
	}

	// 2. 调用业务逻辑
	_, err = service.Food().UpdateFood(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 获取更新后的食物信息
	foodInfo, err := service.Food().GetFoodByID(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.FoodUpdateRes{
		Id:         foodInfo.Id,
		Name:       foodInfo.Name,
		Measure:    foodInfo.Measure,
		Grams:      foodInfo.Grams,
		Calories:   foodInfo.Calories,
		Protein:    foodInfo.Protein,
		Fat:        foodInfo.Fat,
		SatFat:     foodInfo.SatFat,
		Carbs:      foodInfo.Carbs,
		Category:   foodInfo.Category,
		CategoryId: foodInfo.CategoryId,
		ImageUrl:   foodInfo.ImageUrl,
		Unit:       req.Unit,
		Desc:       req.Desc,
		Remark:     req.Remark,
	}

	return res, nil
}

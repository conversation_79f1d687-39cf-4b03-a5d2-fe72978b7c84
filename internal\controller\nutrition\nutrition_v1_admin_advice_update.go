package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) AdminAdviceUpdate(ctx context.Context, req *v1.AdminAdviceUpdateReq) (res *v1.AdminAdviceUpdateRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.NutritionAdviceUpdateInput{
		Id:            req.Id,
		Type:          req.Type,
		Title:         req.Title,
		Description:   req.Description,
		ConditionType: req.ConditionType,
		MinPercentage: req.MinPercentage,
		MaxPercentage: req.MaxPercentage,
		IsDefault:     req.IsDefault,
		Priority:      req.Priority,
		Status:        req.Status,
	}

	// 2. 调用业务逻辑
	advice, err := service.Nutrition().UpdateAdvice(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.AdminAdviceUpdateRes{
		Id:            advice.Id,
		Type:          advice.Type,
		Title:         advice.Title,
		Description:   advice.Description,
		ConditionType: advice.ConditionType,
		MinPercentage: advice.MinPercentage,
		MaxPercentage: advice.MaxPercentage,
		IsDefault:     advice.IsDefault,
		Priority:      advice.Priority,
		Status:        advice.Status,
		CreatedAt:     advice.CreatedAt.String(),
		UpdatedAt:     advice.UpdatedAt.String(),
	}

	return res, nil
}

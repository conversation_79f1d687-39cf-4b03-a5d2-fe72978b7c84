package dashboard

import (
	"context"

	"shikeyinxiang-goframe/api/dashboard/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) PopularFoods(ctx context.Context, req *v1.PopularFoodsReq) (res *v1.PopularFoodsRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.DashboardPopularFoodsInput{
		Period: req.Period,
		Limit:  req.Limit,
	}

	// 2. 调用业务逻辑
	output, err := service.Dashboard().GetPopularFoods(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.PopularFoodsRes{
		Period: req.Period,
		Foods:  convertPopularFoods(output.Foods),
	}

	return res, nil
}

// convertPopularFoods 转换热门食物数据格式
func convertPopularFoods(foods []model.PopularFood) []v1.PopularFood {
	result := make([]v1.PopularFood, len(foods))
	for i, food := range foods {
		result[i] = v1.PopularFood{
			FoodId:       food.FoodId,
			FoodName:     food.FoodName,
			CategoryName: food.CategoryName,
			UsageCount:   food.UsageCount,
			UserCount:    food.UserCount,
			AvgCalorie:   float64(food.Calorie),
		}
	}
	return result
}

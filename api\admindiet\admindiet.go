// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admindiet

import (
	"context"

	"shikeyinxiang-goframe/api/admindiet/v1"
)

type IAdmindietV1 interface {
	DietRecordList(ctx context.Context, req *v1.DietRecordListReq) (res *v1.DietRecordListRes, err error)
	DietRecordDetail(ctx context.Context, req *v1.DietRecordDetailReq) (res *v1.DietRecordDetailRes, err error)
}

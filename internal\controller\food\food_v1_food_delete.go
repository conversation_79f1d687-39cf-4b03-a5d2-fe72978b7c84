package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) FoodDelete(ctx context.Context, req *v1.FoodDeleteReq) (res *v1.FoodDeleteRes, err error) {
	// 1. 调用业务逻辑 - 直接删除食物
	err = service.Food().DeleteFood(ctx, req.Id)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 2. 参数转换：业务层Output → API响应
	res = &v1.FoodDeleteRes{
		Success: true,
		Message: "删除成功",
	}

	return res, nil
}
